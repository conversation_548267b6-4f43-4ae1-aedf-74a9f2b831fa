<?php
/**
 * Page Functionality Tester for Flori Construction Ltd Website
 * 
 * This script tests individual page functionality and content loading
 */

// Suppress errors for testing
error_reporting(0);
ini_set('display_errors', 0);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Functionality Test - Flori Construction</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .test-item { padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .test-item.success { border-left-color: #28a745; }
        .test-item.error { border-left-color: #dc3545; }
        .test-item.warning { border-left-color: #ffc107; }
        h1, h2, h3 { color: #333; }
        .status-icon { font-size: 1.2em; margin-right: 8px; }
        .btn { padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .page-preview { max-height: 200px; overflow: hidden; border: 1px solid #ddd; margin: 10px 0; }
        .page-preview iframe { width: 100%; height: 200px; border: none; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 Page Functionality Test Results</h1>
        <p><strong>Test Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <?php
        $tests_passed = 0;
        $tests_failed = 0;
        $tests_warning = 0;
        
        // Define pages to test
        $pages_to_test = [
            'index.php' => 'Homepage',
            'services.php' => 'Services Page',
            'contact.php' => 'Contact Page',
            'projects.php' => 'Projects Page',
            'admin/login.php' => 'Admin Login',
            'setup.php' => 'Setup Wizard'
        ];
        
        echo '<div class="test-section">';
        echo '<h2>1. Page Loading Tests</h2>';
        echo '<div class="test-grid">';
        
        foreach ($pages_to_test as $page => $name) {
            echo '<div class="test-item-container">';
            
            // Test if file exists
            if (file_exists($page)) {
                // Test if page loads without fatal errors
                ob_start();
                $error_occurred = false;
                
                try {
                    // Capture any output and errors
                    $old_error_handler = set_error_handler(function($errno, $errstr, $errfile, $errline) use (&$error_occurred) {
                        $error_occurred = true;
                        return true;
                    });
                    
                    // Try to include the page
                    include $page;
                    
                    restore_error_handler();
                    $output = ob_get_clean();
                    
                    if (!$error_occurred && strlen($output) > 100) {
                        echo '<div class="test-item success">';
                        echo '<span class="status-icon">✅</span><strong>' . $name . ':</strong> Loads successfully';
                        echo '<br><small>Output length: ' . strlen($output) . ' characters</small>';
                        echo '</div>';
                        $tests_passed++;
                    } else if ($error_occurred) {
                        echo '<div class="test-item error">';
                        echo '<span class="status-icon">❌</span><strong>' . $name . ':</strong> PHP errors detected';
                        echo '</div>';
                        $tests_failed++;
                    } else {
                        echo '<div class="test-item warning">';
                        echo '<span class="status-icon">⚠️</span><strong>' . $name . ':</strong> Minimal output (possible issue)';
                        echo '<br><small>Output length: ' . strlen($output) . ' characters</small>';
                        echo '</div>';
                        $tests_warning++;
                    }
                    
                } catch (Exception $e) {
                    ob_end_clean();
                    echo '<div class="test-item error">';
                    echo '<span class="status-icon">❌</span><strong>' . $name . ':</strong> Exception - ' . $e->getMessage();
                    echo '</div>';
                    $tests_failed++;
                } catch (Error $e) {
                    ob_end_clean();
                    echo '<div class="test-item error">';
                    echo '<span class="status-icon">❌</span><strong>' . $name . ':</strong> Fatal Error - ' . $e->getMessage();
                    echo '</div>';
                    $tests_failed++;
                }
                
            } else {
                echo '<div class="test-item error">';
                echo '<span class="status-icon">❌</span><strong>' . $name . ':</strong> File not found';
                echo '</div>';
                $tests_failed++;
            }
            
            echo '</div>';
        }
        
        echo '</div>';
        echo '</div>';
        
        // Test 2: Database-dependent functionality
        echo '<div class="test-section">';
        echo '<h2>2. Database Functionality Tests</h2>';
        
        try {
            // Check if database config exists
            $config_file = '';
            if (file_exists('config/database_config.php')) {
                $config_file = 'config/database_config.php';
            } elseif (file_exists('config/database.php')) {
                $config_file = 'config/database.php';
            }
            
            if ($config_file) {
                require_once $config_file;
                
                // Test database connection
                $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . (defined('DB_CHARSET') ? DB_CHARSET : 'utf8mb4');
                $pdo = new PDO($dsn, DB_USER, DB_PASS, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                ]);
                
                echo '<div class="test-grid">';
                
                // Test services data
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM services WHERE is_active = 1");
                    $services_count = $stmt->fetch()['count'];
                    
                    if ($services_count > 0) {
                        echo '<div class="test-item success">';
                        echo '<span class="status-icon">✅</span><strong>Services Data:</strong> ' . $services_count . ' active services';
                        echo '</div>';
                        $tests_passed++;
                    } else {
                        echo '<div class="test-item warning">';
                        echo '<span class="status-icon">⚠️</span><strong>Services Data:</strong> No active services found';
                        echo '</div>';
                        $tests_warning++;
                    }
                } catch (Exception $e) {
                    echo '<div class="test-item error">';
                    echo '<span class="status-icon">❌</span><strong>Services Table:</strong> ' . $e->getMessage();
                    echo '</div>';
                    $tests_failed++;
                }
                
                // Test projects data
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM projects WHERE status = 'completed'");
                    $projects_count = $stmt->fetch()['count'];
                    
                    if ($projects_count > 0) {
                        echo '<div class="test-item success">';
                        echo '<span class="status-icon">✅</span><strong>Projects Data:</strong> ' . $projects_count . ' completed projects';
                        echo '</div>';
                        $tests_passed++;
                    } else {
                        echo '<div class="test-item warning">';
                        echo '<span class="status-icon">⚠️</span><strong>Projects Data:</strong> No completed projects found';
                        echo '</div>';
                        $tests_warning++;
                    }
                } catch (Exception $e) {
                    echo '<div class="test-item error">';
                    echo '<span class="status-icon">❌</span><strong>Projects Table:</strong> ' . $e->getMessage();
                    echo '</div>';
                    $tests_failed++;
                }
                
                // Test admin users
                try {
                    $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users WHERE is_active = 1");
                    $admin_count = $stmt->fetch()['count'];
                    
                    if ($admin_count > 0) {
                        echo '<div class="test-item success">';
                        echo '<span class="status-icon">✅</span><strong>Admin Users:</strong> ' . $admin_count . ' active admin(s)';
                        echo '</div>';
                        $tests_passed++;
                    } else {
                        echo '<div class="test-item warning">';
                        echo '<span class="status-icon">⚠️</span><strong>Admin Users:</strong> No active admin users found';
                        echo '</div>';
                        $tests_warning++;
                    }
                } catch (Exception $e) {
                    echo '<div class="test-item error">';
                    echo '<span class="status-icon">❌</span><strong>Admin Users Table:</strong> ' . $e->getMessage();
                    echo '</div>';
                    $tests_failed++;
                }
                
                echo '</div>';
                
            } else {
                echo '<div class="test-item error">';
                echo '<span class="status-icon">❌</span><strong>Database Config:</strong> Configuration file not found';
                echo '</div>';
                $tests_failed++;
            }
            
        } catch (Exception $e) {
            echo '<div class="test-item error">';
            echo '<span class="status-icon">❌</span><strong>Database Connection:</strong> ' . $e->getMessage();
            echo '</div>';
            $tests_failed++;
        }
        
        echo '</div>';
        
        // Test 3: Include Files
        echo '<div class="test-section">';
        echo '<h2>3. Include Files Tests</h2>';
        echo '<div class="test-grid">';
        
        $include_files = [
            'includes/header.php' => 'Header Include',
            'includes/footer.php' => 'Footer Include',
            'includes/functions.php' => 'Functions Include',
            'includes/security.php' => 'Security Include',
            'config/config.php' => 'Main Config',
            'config/database.php' => 'Database Config'
        ];
        
        foreach ($include_files as $file => $name) {
            if (file_exists($file)) {
                // Check if file has content
                $content = file_get_contents($file);
                if (strlen($content) > 50) {
                    echo '<div class="test-item success">';
                    echo '<span class="status-icon">✅</span><strong>' . $name . ':</strong> Found (' . strlen($content) . ' bytes)';
                    echo '</div>';
                    $tests_passed++;
                } else {
                    echo '<div class="test-item warning">';
                    echo '<span class="status-icon">⚠️</span><strong>' . $name . ':</strong> File too small';
                    echo '</div>';
                    $tests_warning++;
                }
            } else {
                echo '<div class="test-item error">';
                echo '<span class="status-icon">❌</span><strong>' . $name . ':</strong> File missing';
                echo '</div>';
                $tests_failed++;
            }
        }
        
        echo '</div>';
        echo '</div>';
        ?>
        
        <!-- Test Summary -->
        <div class="test-section">
            <h2>📊 Test Summary</h2>
            <div class="test-grid">
                <div class="test-item success">
                    <span class="status-icon">✅</span><strong>Passed:</strong> <?php echo $tests_passed; ?> tests
                </div>
                <div class="test-item error">
                    <span class="status-icon">❌</span><strong>Failed:</strong> <?php echo $tests_failed; ?> tests
                </div>
                <div class="test-item warning">
                    <span class="status-icon">⚠️</span><strong>Warnings:</strong> <?php echo $tests_warning; ?> tests
                </div>
                <div class="test-item info">
                    <span class="status-icon">📈</span><strong>Total:</strong> <?php echo $tests_passed + $tests_failed + $tests_warning; ?> tests
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="test-section">
            <h3>🚀 Quick Actions</h3>
            <a href="test_functionality.php" class="btn btn-primary">Full System Test</a>
            <a href="setup.php" class="btn btn-success">Run Setup</a>
            <a href="index.php" class="btn btn-info">View Homepage</a>
            <a href="admin/login.php" class="btn btn-warning">Admin Login</a>
        </div>
        
        <!-- Page Previews -->
        <div class="test-section">
            <h3>🖼️ Page Previews</h3>
            <p><strong>Note:</strong> Click the links above to view pages in full. The previews below show if pages are loading:</p>
            
            <div class="test-grid">
                <div>
                    <h4>Homepage</h4>
                    <div class="page-preview">
                        <iframe src="index.php" title="Homepage Preview"></iframe>
                    </div>
                    <a href="index.php" target="_blank" class="btn btn-primary">View Full Page</a>
                </div>
                
                <div>
                    <h4>Admin Login</h4>
                    <div class="page-preview">
                        <iframe src="admin/login.php" title="Admin Login Preview"></iframe>
                    </div>
                    <a href="admin/login.php" target="_blank" class="btn btn-primary">View Full Page</a>
                </div>
            </div>
        </div>
        
    </div>
</body>
</html>
