<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? escapeOutput($page_title) : 'Admin Panel'; ?> - <?php echo SITE_NAME; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Admin Stylesheet -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/admin.css">
    
    <!-- Additional CSS for specific pages -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/<?php echo $css_file; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            background: #f8fafc;
            color: #1f2937;
            line-height: 1.6;
        }
        
        .admin-layout {
            display: flex;
            min-height: 100vh;
        }
        
        .admin-sidebar {
            width: 260px;
            background: #1f2937;
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
        }
        
        .admin-main {
            flex: 1;
            margin-left: 260px;
            min-height: 100vh;
        }
        
        .admin-header {
            background: white;
            padding: 15px 30px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        
        .admin-content {
            padding: 30px;
        }
        
        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #374151;
            text-align: center;
        }
        
        .sidebar-header h2 {
            color: white;
            font-size: 1.25rem;
            font-weight: 600;
        }
        
        .sidebar-nav {
            padding: 20px 0;
        }
        
        .nav-item {
            margin-bottom: 5px;
        }
        
        .nav-link {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 20px;
            color: #d1d5db;
            text-decoration: none;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
        }
        
        .nav-link:hover,
        .nav-link.active {
            background: #374151;
            color: white;
            border-left-color: #3b82f6;
        }
        
        .nav-link i {
            width: 20px;
            text-align: center;
        }
        
        .user-menu {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .user-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .user-avatar {
            width: 35px;
            height: 35px;
            background: #3b82f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }
        
        .user-details {
            display: flex;
            flex-direction: column;
        }
        
        .user-name {
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .user-role {
            font-size: 0.8rem;
            color: #6b7280;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-sm {
            padding: 6px 12px;
            font-size: 0.8rem;
        }
        
        .btn-primary {
            background: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background: #2563eb;
        }
        
        .btn-secondary {
            background: #6b7280;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .btn-outline {
            background: transparent;
            color: #3b82f6;
            border: 1px solid #3b82f6;
        }
        
        .btn-outline:hover {
            background: #3b82f6;
            color: white;
        }
        
        .page-header {
            margin-bottom: 30px;
        }
        
        .page-header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .page-header p {
            color: #6b7280;
            font-size: 1.1rem;
        }
        
        .mobile-menu-toggle {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #6b7280;
            cursor: pointer;
        }
        
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .admin-sidebar.active {
                transform: translateX(0);
            }
            
            .admin-main {
                margin-left: 0;
            }
            
            .mobile-menu-toggle {
                display: block;
            }
            
            .admin-content {
                padding: 20px 15px;
            }
            
            .admin-header {
                padding: 15px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="admin-layout">
        <!-- Sidebar -->
        <aside class="admin-sidebar" id="admin-sidebar">
            <div class="sidebar-header">
                <h2><i class="fas fa-cog"></i> Admin Panel</h2>
            </div>
            
            <nav class="sidebar-nav">
                <div class="nav-item">
                    <a href="dashboard.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'dashboard.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tachometer-alt"></i>
                        <span>Dashboard</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="projects.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'projects.php' ? 'active' : ''; ?>">
                        <i class="fas fa-building"></i>
                        <span>Projects</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="services.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'services.php' ? 'active' : ''; ?>">
                        <i class="fas fa-tools"></i>
                        <span>Services</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="gallery.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'gallery.php' ? 'active' : ''; ?>">
                        <i class="fas fa-images"></i>
                        <span>Gallery</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="inquiries.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'inquiries.php' ? 'active' : ''; ?>">
                        <i class="fas fa-envelope"></i>
                        <span>Inquiries</span>
                        <?php
                        $new_inquiries = $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'new'")['count'];
                        if ($new_inquiries > 0):
                        ?>
                        <span class="badge"><?php echo $new_inquiries; ?></span>
                        <?php endif; ?>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="testimonials.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'testimonials.php' ? 'active' : ''; ?>">
                        <i class="fas fa-star"></i>
                        <span>Testimonials</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="content.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'content.php' ? 'active' : ''; ?>">
                        <i class="fas fa-edit"></i>
                        <span>Content</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="settings.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) === 'settings.php' ? 'active' : ''; ?>">
                        <i class="fas fa-cog"></i>
                        <span>Settings</span>
                    </a>
                </div>
                
                <div class="nav-item" style="margin-top: 20px; border-top: 1px solid #374151; padding-top: 20px;">
                    <a href="<?php echo SITE_URL; ?>" class="nav-link" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        <span>View Website</span>
                    </a>
                </div>
                
                <div class="nav-item">
                    <a href="logout.php" class="nav-link" onclick="return confirm('Are you sure you want to logout?')">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>Logout</span>
                    </a>
                </div>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="admin-main">
            <!-- Header -->
            <header class="admin-header">
                <div class="header-left">
                    <button class="mobile-menu-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
                
                <div class="header-actions">
                    <a href="<?php echo SITE_URL; ?>" class="btn btn-outline" target="_blank">
                        <i class="fas fa-external-link-alt"></i>
                        View Website
                    </a>
                    
                    <div class="user-menu">
                        <div class="user-info">
                            <div class="user-avatar">
                                <?php echo strtoupper(substr($current_admin['full_name'], 0, 1)); ?>
                            </div>
                            <div class="user-details">
                                <div class="user-name"><?php echo escapeOutput($current_admin['full_name']); ?></div>
                                <div class="user-role"><?php echo ucfirst($current_admin['role']); ?></div>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Flash Messages -->
            <?php 
            $flash_message = getFlashMessage();
            if ($flash_message): 
            ?>
            <div class="flash-message flash-<?php echo $flash_message['type']; ?>" style="
                margin: 20px 30px 0;
                padding: 15px 20px;
                border-radius: 6px;
                font-weight: 500;
                <?php 
                $colors = [
                    'success' => 'background: #d1fae5; color: #065f46; border: 1px solid #a7f3d0;',
                    'error' => 'background: #fee2e2; color: #991b1b; border: 1px solid #fecaca;',
                    'warning' => 'background: #fef3c7; color: #92400e; border: 1px solid #fde68a;',
                    'info' => 'background: #dbeafe; color: #1e40af; border: 1px solid #93c5fd;'
                ];
                echo $colors[$flash_message['type']] ?? $colors['info'];
                ?>
            ">
                <i class="fas fa-<?php echo $flash_message['type'] === 'error' ? 'exclamation-triangle' : ($flash_message['type'] === 'success' ? 'check-circle' : 'info-circle'); ?>"></i>
                <?php echo escapeOutput($flash_message['text']); ?>
                <button onclick="this.parentElement.remove()" style="
                    background: none;
                    border: none;
                    float: right;
                    cursor: pointer;
                    font-size: 16px;
                    opacity: 0.7;
                ">&times;</button>
            </div>
            <?php endif; ?>

            <style>
            .badge {
                background: #ef4444;
                color: white;
                font-size: 0.7rem;
                padding: 2px 6px;
                border-radius: 10px;
                margin-left: auto;
            }
            
            .flash-message {
                display: flex;
                align-items: center;
                gap: 10px;
            }
            </style>

            <script>
            function toggleSidebar() {
                const sidebar = document.getElementById('admin-sidebar');
                sidebar.classList.toggle('active');
            }
            
            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function(e) {
                const sidebar = document.getElementById('admin-sidebar');
                const toggle = document.querySelector('.mobile-menu-toggle');
                
                if (window.innerWidth <= 768 && 
                    !sidebar.contains(e.target) && 
                    !toggle.contains(e.target) && 
                    sidebar.classList.contains('active')) {
                    sidebar.classList.remove('active');
                }
            });
            </script>
