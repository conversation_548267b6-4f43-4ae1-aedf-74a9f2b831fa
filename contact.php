<?php
/**
 * Contact Page for Flori Construction Ltd Website
 * 
 * This page displays contact information and a contact form
 * for potential clients to get in touch.
 */

require_once 'config/config.php';

// Get pre-selected service if provided
$selected_service = isset($_GET['service']) ? sanitizeInput($_GET['service']) : '';

// Get all services for the dropdown
$db = getDB();
$services = $db->fetchAll(
    "SELECT title FROM services WHERE is_active = 1 ORDER BY sort_order ASC"
);

$page_title = 'Contact Us - ' . SITE_NAME;
$page_description = 'Get in touch with Flori Construction Ltd for your construction project. Contact us for a free consultation and quote.';

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="page-header" style="
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(29, 78, 216, 0.9)), 
                url('assets/images/contact-header-bg.jpg') center/cover;
    padding: 120px 0 80px;
    color: white;
    text-align: center;
">
    <div class="container">
        <h1>Contact Us</h1>
        <p style="font-size: 1.2rem; max-width: 600px; margin: 20px auto 0;">Ready to start your construction project? Get in touch with our experienced team today.</p>
    </div>
</section>

<!-- Contact Section -->
<section class="section contact">
    <div class="container">
        <div class="row">
            <!-- Contact Form -->
            <div class="col-8">
                <div class="contact-form-container">
                    <h2>Send Us a Message</h2>
                    <p>Fill out the form below and we'll get back to you as soon as possible with a detailed response to your inquiry.</p>
                    
                    <form id="contact-form" class="contact-form" method="POST" action="contact_handler.php">
                        <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="name">Full Name *</label>
                                <input type="text" id="name" name="name" required data-label="Full Name">
                            </div>
                            <div class="form-group">
                                <label for="email">Email Address *</label>
                                <input type="email" id="email" name="email" required data-label="Email Address">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="phone">Phone Number</label>
                                <input type="tel" id="phone" name="phone" data-label="Phone Number">
                            </div>
                            <div class="form-group">
                                <label for="company">Company Name</label>
                                <input type="text" id="company" name="company" data-label="Company Name">
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="service_interest">Service of Interest</label>
                                <select id="service_interest" name="service_interest">
                                    <option value="">Select a service...</option>
                                    <?php foreach ($services as $service): ?>
                                    <option value="<?php echo escapeOutput($service['title']); ?>" 
                                            <?php echo $selected_service === $service['title'] ? 'selected' : ''; ?>>
                                        <?php echo escapeOutput($service['title']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                    <option value="Other">Other</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="subject">Subject</label>
                                <input type="text" id="subject" name="subject" data-label="Subject">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">Message *</label>
                            <textarea id="message" name="message" rows="6" required data-label="Message" 
                                      placeholder="Please provide details about your project, timeline, and any specific requirements..."></textarea>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">Send Message</button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Contact Information -->
            <div class="col-4">
                <div class="contact-info">
                    <h3>Get In Touch</h3>
                    <p>We're here to help with your construction needs. Contact us using any of the methods below.</p>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-details">
                            <h4>Our Office</h4>
                            <p><?php echo escapeOutput(getCompanyInfo('address_full')); ?></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="contact-details">
                            <h4>Phone Numbers</h4>
                            <p><a href="tel:<?php echo escapeOutput(getCompanyInfo('phone_primary')); ?>"><?php echo escapeOutput(getCompanyInfo('phone_primary')); ?></a></p>
                            <?php if (getCompanyInfo('phone_mobile')): ?>
                            <p><a href="tel:<?php echo escapeOutput(getCompanyInfo('phone_mobile')); ?>"><?php echo escapeOutput(getCompanyInfo('phone_mobile')); ?></a></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-details">
                            <h4>Email Address</h4>
                            <p><a href="mailto:<?php echo escapeOutput(getCompanyInfo('email_primary')); ?>"><?php echo escapeOutput(getCompanyInfo('email_primary')); ?></a></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">
                            <i class="fas fa-clock"></i>
                        </div>
                        <div class="contact-details">
                            <h4>Business Hours</h4>
                            <p>Monday - Friday: 8:00 AM - 6:00 PM</p>
                            <p>Saturday: 9:00 AM - 4:00 PM</p>
                            <p>Sunday: Closed</p>
                        </div>
                    </div>
                    
                    <div class="social-links-contact">
                        <h4>Follow Us</h4>
                        <div class="social-icons">
                            <?php if (getCompanyInfo('facebook_url')): ?>
                            <a href="<?php echo escapeOutput(getCompanyInfo('facebook_url')); ?>" target="_blank" rel="noopener">
                                <i class="fab fa-facebook-f"></i>
                            </a>
                            <?php endif; ?>
                            <?php if (getCompanyInfo('instagram_url')): ?>
                            <a href="<?php echo escapeOutput(getCompanyInfo('instagram_url')); ?>" target="_blank" rel="noopener">
                                <i class="fab fa-instagram"></i>
                            </a>
                            <?php endif; ?>
                            <?php if (getCompanyInfo('linkedin_url')): ?>
                            <a href="<?php echo escapeOutput(getCompanyInfo('linkedin_url')); ?>" target="_blank" rel="noopener">
                                <i class="fab fa-linkedin-in"></i>
                            </a>
                            <?php endif; ?>
                            <?php if (getCompanyInfo('youtube_url')): ?>
                            <a href="<?php echo escapeOutput(getCompanyInfo('youtube_url')); ?>" target="_blank" rel="noopener">
                                <i class="fab fa-youtube"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="map-section">
    <div class="container-fluid">
        <div class="map-container">
            <iframe 
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d2476.8234567890123!2d-0.1234567890123456!3d51.61234567890123!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x0%3A0x0!2zNTHCsDM2JzQ0LjQiTiAwwrAwNyc0NC40Ilc!5e0!3m2!1sen!2suk!4v1234567890123"
                width="100%" 
                height="400" 
                style="border:0;" 
                allowfullscreen="" 
                loading="lazy" 
                referrerpolicy="no-referrer-when-downgrade"
                title="Flori Construction Ltd Location">
            </iframe>
        </div>
    </div>
</section>

<style>
.contact-form-container {
    background: #fff;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    margin-bottom: 40px;
}

.contact-form-container h2 {
    color: #1f2937;
    margin-bottom: 10px;
}

.contact-form-container p {
    color: #6b7280;
    margin-bottom: 30px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    flex: 1;
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e5e7eb;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2563eb;
}

.contact-info {
    background: #f9fafb;
    padding: 40px 30px;
    border-radius: 10px;
    height: fit-content;
}

.contact-info h3 {
    color: #1f2937;
    margin-bottom: 15px;
}

.contact-info > p {
    color: #6b7280;
    margin-bottom: 30px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 15px;
    margin-bottom: 30px;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: #2563eb;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h4 {
    color: #1f2937;
    margin-bottom: 8px;
    font-size: 1.1rem;
}

.contact-details p {
    color: #6b7280;
    margin-bottom: 5px;
    line-height: 1.5;
}

.contact-details a {
    color: #2563eb;
    text-decoration: none;
}

.contact-details a:hover {
    text-decoration: underline;
}

.social-links-contact {
    margin-top: 30px;
}

.social-links-contact h4 {
    color: #1f2937;
    margin-bottom: 15px;
}

.social-icons {
    display: flex;
    gap: 10px;
}

.social-icons a {
    width: 40px;
    height: 40px;
    background: #2563eb;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-icons a:hover {
    background: #1d4ed8;
    transform: translateY(-2px);
}

.map-container {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .contact-form-container {
        padding: 30px 20px;
    }
    
    .contact-info {
        padding: 30px 20px;
        margin-top: 30px;
    }
    
    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .contact-icon {
        align-self: center;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
