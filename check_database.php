<?php
/**
 * Database Checker for Flori Construction Ltd Website
 *
 * This script helps debug database connection and table issues
 */

echo "<h2>Database Connection Checker</h2>";

// Check if config file exists - try both possible config files
$config_file = '';
if (file_exists('config/database_config.php')) {
    $config_file = 'config/database_config.php';
} elseif (file_exists('config/database.php')) {
    $config_file = 'config/database.php';
} else {
    echo "<p style='color: red;'>❌ Database config file not found. Please run setup first.</p>";
    echo "<p>Looking for: config/database_config.php or config/database.php</p>";
    exit;
}

require_once $config_file;

echo "<h3>Configuration:</h3>";
echo "<ul>";
echo "<li><strong>Host:</strong> " . DB_HOST . "</li>";
echo "<li><strong>Database:</strong> " . DB_NAME . "</li>";
echo "<li><strong>User:</strong> " . DB_USER . "</li>";
echo "<li><strong>Charset:</strong> " . DB_CHARSET . "</li>";
echo "</ul>";

try {
    // Test connection without database
    $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;
    $pdo = new PDO($dsn, DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
    ]);
    echo "<p style='color: green;'>✅ MySQL connection successful</p>";
    
    // Check if database exists
    $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
    $db_exists = $stmt->rowCount() > 0;
    
    if ($db_exists) {
        echo "<p style='color: green;'>✅ Database '" . DB_NAME . "' exists</p>";
        
        // Connect to the specific database
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS, [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
        ]);
        
        echo "<p style='color: green;'>✅ Connected to database '" . DB_NAME . "'</p>";
        
        // Check tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        echo "<h3>Tables in database:</h3>";
        if (empty($tables)) {
            echo "<p style='color: orange;'>⚠️ No tables found in database</p>";
        } else {
            echo "<ul>";
            foreach ($tables as $table) {
                echo "<li>$table</li>";
            }
            echo "</ul>";
            
            // Check if admin_users table exists and has data
            if (in_array('admin_users', $tables)) {
                $stmt = $pdo->query("SELECT COUNT(*) as count FROM admin_users");
                $count = $stmt->fetch()['count'];
                echo "<p style='color: green;'>✅ admin_users table exists with $count records</p>";
                
                if ($count > 0) {
                    $stmt = $pdo->query("SELECT username, email, full_name FROM admin_users LIMIT 5");
                    $users = $stmt->fetchAll();
                    echo "<h4>Admin users:</h4><ul>";
                    foreach ($users as $user) {
                        echo "<li>{$user['username']} ({$user['email']}) - {$user['full_name']}</li>";
                    }
                    echo "</ul>";
                }
            } else {
                echo "<p style='color: red;'>❌ admin_users table not found</p>";
            }
        }
        
    } else {
        echo "<p style='color: red;'>❌ Database '" . DB_NAME . "' does not exist</p>";
        echo "<p>Available databases:</p>";
        $stmt = $pdo->query("SHOW DATABASES");
        $databases = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<ul>";
        foreach ($databases as $db) {
            if (!in_array($db, ['information_schema', 'mysql', 'performance_schema', 'sys'])) {
                echo "<li>$db</li>";
            }
        }
        echo "</ul>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>Quick Actions:</h3>";
echo "<ul>";
echo "<li><a href='setup.php'>Run Setup Wizard</a></li>";
echo "<li><a href='setup.php?step=2'>Go to Table Creation Step</a></li>";
echo "<li><a href='index.php'>View Website</a></li>";
echo "<li><a href='admin/login.php'>Admin Login</a></li>";
echo "</ul>";
?>
