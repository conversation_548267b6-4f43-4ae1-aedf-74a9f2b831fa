<?php
/**
 * Admin Dashboard for Flori Construction Ltd Website
 * 
 * This page displays the main admin dashboard with overview statistics
 * and quick access to management functions.
 */

require_once '../config/config.php';
requireAdminLogin();

$current_admin = getCurrentAdmin();

// Get dashboard statistics
$db = getDB();

// Count statistics
$stats = [
    'total_projects' => $db->fetchOne("SELECT COUNT(*) as count FROM projects")['count'],
    'completed_projects' => $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE status = 'completed'")['count'],
    'ongoing_projects' => $db->fetchOne("SELECT COUNT(*) as count FROM projects WHERE status = 'ongoing'")['count'],
    'total_services' => $db->fetchOne("SELECT COUNT(*) as count FROM services WHERE is_active = 1")['count'],
    'new_inquiries' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries WHERE status = 'new'")['count'],
    'total_inquiries' => $db->fetchOne("SELECT COUNT(*) as count FROM contact_inquiries")['count'],
    'gallery_images' => $db->fetchOne("SELECT COUNT(*) as count FROM gallery_images WHERE is_active = 1")['count'],
    'testimonials' => $db->fetchOne("SELECT COUNT(*) as count FROM testimonials WHERE is_active = 1")['count']
];

// Recent inquiries
$recent_inquiries = $db->fetchAll(
    "SELECT * FROM contact_inquiries ORDER BY created_at DESC LIMIT 5"
);

// Recent projects
$recent_projects = $db->fetchAll(
    "SELECT p.*, pc.name as category_name 
     FROM projects p 
     LEFT JOIN project_categories pc ON p.category_id = pc.id 
     ORDER BY p.created_at DESC LIMIT 5"
);

$page_title = 'Dashboard - Admin Panel';
include 'includes/admin_header.php';
?>

<div class="admin-content">
    <div class="page-header">
        <h1><i class="fas fa-tachometer-alt"></i> Dashboard</h1>
        <p>Welcome back, <?php echo escapeOutput($current_admin['full_name']); ?>!</p>
    </div>

    <!-- Statistics Cards -->
    <div class="stats-grid">
        <div class="stat-card">
            <div class="stat-icon projects">
                <i class="fas fa-building"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats['total_projects']; ?></h3>
                <p>Total Projects</p>
                <small><?php echo $stats['completed_projects']; ?> completed, <?php echo $stats['ongoing_projects']; ?> ongoing</small>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon services">
                <i class="fas fa-tools"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats['total_services']; ?></h3>
                <p>Active Services</p>
                <small>Available on website</small>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon inquiries">
                <i class="fas fa-envelope"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats['new_inquiries']; ?></h3>
                <p>New Inquiries</p>
                <small><?php echo $stats['total_inquiries']; ?> total inquiries</small>
            </div>
        </div>

        <div class="stat-card">
            <div class="stat-icon gallery">
                <i class="fas fa-images"></i>
            </div>
            <div class="stat-content">
                <h3><?php echo $stats['gallery_images']; ?></h3>
                <p>Gallery Images</p>
                <small><?php echo $stats['testimonials']; ?> testimonials</small>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
        <h2><i class="fas fa-bolt"></i> Quick Actions</h2>
        <div class="action-buttons">
            <a href="projects.php?action=add" class="action-btn">
                <i class="fas fa-plus"></i>
                <span>Add New Project</span>
            </a>
            <a href="services.php?action=add" class="action-btn">
                <i class="fas fa-plus"></i>
                <span>Add New Service</span>
            </a>
            <a href="gallery.php?action=upload" class="action-btn">
                <i class="fas fa-upload"></i>
                <span>Upload Images</span>
            </a>
            <a href="content.php" class="action-btn">
                <i class="fas fa-edit"></i>
                <span>Edit Content</span>
            </a>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="dashboard-grid">
        <!-- Recent Inquiries -->
        <div class="dashboard-section">
            <div class="section-header">
                <h2><i class="fas fa-envelope-open"></i> Recent Inquiries</h2>
                <a href="inquiries.php" class="btn btn-sm">View All</a>
            </div>
            <div class="section-content">
                <?php if (empty($recent_inquiries)): ?>
                <p class="no-data">No inquiries yet.</p>
                <?php else: ?>
                <div class="inquiry-list">
                    <?php foreach ($recent_inquiries as $inquiry): ?>
                    <div class="inquiry-item <?php echo $inquiry['status'] === 'new' ? 'new' : ''; ?>">
                        <div class="inquiry-header">
                            <strong><?php echo escapeOutput($inquiry['name']); ?></strong>
                            <span class="inquiry-date"><?php echo formatDate($inquiry['created_at'], 'M j, Y'); ?></span>
                        </div>
                        <div class="inquiry-details">
                            <p><?php echo escapeOutput($inquiry['email']); ?></p>
                            <?php if ($inquiry['service_interest']): ?>
                            <span class="service-tag"><?php echo escapeOutput($inquiry['service_interest']); ?></span>
                            <?php endif; ?>
                        </div>
                        <div class="inquiry-message">
                            <?php echo escapeOutput(truncateText($inquiry['message'], 100)); ?>
                        </div>
                        <div class="inquiry-actions">
                            <a href="inquiries.php?action=view&id=<?php echo $inquiry['id']; ?>" class="btn btn-sm">View</a>
                            <?php if ($inquiry['status'] === 'new'): ?>
                            <a href="inquiries.php?action=reply&id=<?php echo $inquiry['id']; ?>" class="btn btn-sm btn-primary">Reply</a>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Recent Projects -->
        <div class="dashboard-section">
            <div class="section-header">
                <h2><i class="fas fa-building"></i> Recent Projects</h2>
                <a href="projects.php" class="btn btn-sm">View All</a>
            </div>
            <div class="section-content">
                <?php if (empty($recent_projects)): ?>
                <p class="no-data">No projects yet.</p>
                <?php else: ?>
                <div class="project-list">
                    <?php foreach ($recent_projects as $project): ?>
                    <div class="project-item">
                        <div class="project-image">
                            <?php if ($project['featured_image']): ?>
                            <img src="<?php echo escapeOutput($project['featured_image']); ?>" alt="<?php echo escapeOutput($project['title']); ?>">
                            <?php else: ?>
                            <div class="no-image"><i class="fas fa-image"></i></div>
                            <?php endif; ?>
                        </div>
                        <div class="project-details">
                            <h4><?php echo escapeOutput($project['title']); ?></h4>
                            <p class="project-category"><?php echo escapeOutput($project['category_name'] ?: 'Uncategorized'); ?></p>
                            <p class="project-status status-<?php echo $project['status']; ?>">
                                <?php echo ucfirst($project['status']); ?>
                            </p>
                            <div class="project-actions">
                                <a href="projects.php?action=edit&id=<?php echo $project['id']; ?>" class="btn btn-sm">Edit</a>
                                <a href="<?php echo SITE_URL; ?>/project.php?slug=<?php echo $project['slug']; ?>" class="btn btn-sm" target="_blank">View</a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- System Information -->
    <div class="system-info">
        <h2><i class="fas fa-info-circle"></i> System Information</h2>
        <div class="info-grid">
            <div class="info-item">
                <label>PHP Version:</label>
                <span><?php echo PHP_VERSION; ?></span>
            </div>
            <div class="info-item">
                <label>Database:</label>
                <span><?php echo testDatabaseConnection() ? 'Connected' : 'Disconnected'; ?></span>
            </div>
            <div class="info-item">
                <label>Last Login:</label>
                <span><?php echo $current_admin['last_login'] ? formatDate($current_admin['last_login'], 'M j, Y g:i A') : 'First login'; ?></span>
            </div>
            <div class="info-item">
                <label>User Role:</label>
                <span><?php echo ucfirst($current_admin['role']); ?></span>
            </div>
        </div>
    </div>
</div>

<style>
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 20px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.projects { background: #3b82f6; }
.stat-icon.services { background: #10b981; }
.stat-icon.inquiries { background: #f59e0b; }
.stat-icon.gallery { background: #8b5cf6; }

.stat-content h3 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0 0 5px 0;
    color: #1f2937;
}

.stat-content p {
    font-weight: 600;
    color: #6b7280;
    margin: 0 0 5px 0;
}

.stat-content small {
    color: #9ca3af;
    font-size: 0.85rem;
}

.quick-actions {
    background: white;
    border-radius: 10px;
    padding: 25px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.quick-actions h2 {
    margin: 0 0 20px 0;
    color: #1f2937;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    background: #f8fafc;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    text-decoration: none;
    color: #475569;
    font-weight: 500;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #2563eb;
    border-color: #2563eb;
    color: white;
    transform: translateY(-2px);
}

.dashboard-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.dashboard-section {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.section-header {
    padding: 20px 25px;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h2 {
    margin: 0;
    color: #1f2937;
    font-size: 1.25rem;
}

.section-content {
    padding: 20px 25px;
}

.inquiry-item {
    padding: 15px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 15px;
}

.inquiry-item.new {
    border-color: #fbbf24;
    background: #fffbeb;
}

.inquiry-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.inquiry-date {
    color: #6b7280;
    font-size: 0.9rem;
}

.service-tag {
    background: #dbeafe;
    color: #1d4ed8;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.inquiry-message {
    color: #6b7280;
    margin: 10px 0;
    font-size: 0.9rem;
}

.inquiry-actions {
    display: flex;
    gap: 10px;
}

.project-item {
    display: flex;
    gap: 15px;
    padding: 15px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 15px;
}

.project-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.project-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.no-image {
    width: 100%;
    height: 100%;
    background: #f3f4f6;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #9ca3af;
}

.project-details h4 {
    margin: 0 0 5px 0;
    color: #1f2937;
}

.project-category {
    color: #6b7280;
    font-size: 0.9rem;
    margin: 0 0 5px 0;
}

.project-status {
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
    margin: 0 0 10px 0;
}

.status-completed { color: #10b981; }
.status-ongoing { color: #f59e0b; }
.status-planning { color: #6b7280; }

.project-actions {
    display: flex;
    gap: 10px;
}

.system-info {
    background: white;
    border-radius: 10px;
    padding: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.system-info h2 {
    margin: 0 0 20px 0;
    color: #1f2937;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 15px;
    background: #f8fafc;
    border-radius: 6px;
}

.info-item label {
    font-weight: 500;
    color: #6b7280;
}

.info-item span {
    color: #1f2937;
    font-weight: 600;
}

.no-data {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 20px;
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        grid-template-columns: 1fr;
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
}
</style>

<?php include 'includes/admin_footer.php'; ?>
