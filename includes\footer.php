    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <!-- Company Information -->
                <div class="footer-section">
                    <h3><?php echo escapeOutput(getCompanyInfo('company_name')); ?></h3>
                    <p>Our team brings together many years of collective experience in the construction industry embodying extensive knowledge and refined processes.</p>
                    <div class="footer-contact">
                        <p><i class="fas fa-map-marker-alt"></i> <?php echo escapeOutput(getCompanyInfo('address_full')); ?></p>
                        <p><i class="fas fa-phone"></i> <a href="tel:<?php echo escapeOutput(getCompanyInfo('phone_primary')); ?>"><?php echo escapeOutput(getCompanyInfo('phone_primary')); ?></a></p>
                        <p><i class="fas fa-envelope"></i> <a href="mailto:<?php echo escapeOutput(getCompanyInfo('email_primary')); ?>"><?php echo escapeOutput(getCompanyInfo('email_primary')); ?></a></p>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="footer-section">
                    <h3>Company</h3>
                    <ul class="footer-links">
                        <li><a href="<?php echo SITE_URL; ?>">Home</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/about.php">About Us</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/services.php">Our Services</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/projects.php">Our Projects</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/gallery.php">Gallery</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/contact.php">Contact Us</a></li>
                    </ul>
                </div>

                <!-- Services -->
                <div class="footer-section">
                    <h3>Services</h3>
                    <ul class="footer-links">
                        <?php
                        $footer_services = $db->fetchAll(
                            "SELECT title, slug FROM services WHERE is_active = 1 ORDER BY sort_order ASC LIMIT 6"
                        );
                        foreach ($footer_services as $service):
                        ?>
                        <li><a href="<?php echo SITE_URL; ?>/services.php#<?php echo escapeOutput($service['slug']); ?>"><?php echo escapeOutput($service['title']); ?></a></li>
                        <?php endforeach; ?>
                    </ul>
                </div>

                <!-- Contact & Social -->
                <div class="footer-section">
                    <h3>Get In Touch</h3>
                    <p>Ready to start your construction project? Contact us today for a free consultation.</p>
                    
                    <div class="footer-contact-info">
                        <p><strong>Phone:</strong> <?php echo escapeOutput(getCompanyInfo('phone_primary')); ?></p>
                        <?php if (getCompanyInfo('phone_mobile')): ?>
                        <p><strong>Mobile:</strong> <?php echo escapeOutput(getCompanyInfo('phone_mobile')); ?></p>
                        <?php endif; ?>
                        <p><strong>Email:</strong> <?php echo escapeOutput(getCompanyInfo('email_primary')); ?></p>
                    </div>

                    <div class="footer-social">
                        <?php if (getCompanyInfo('facebook_url')): ?>
                        <a href="<?php echo escapeOutput(getCompanyInfo('facebook_url')); ?>" target="_blank" rel="noopener" aria-label="Facebook">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <?php endif; ?>
                        <?php if (getCompanyInfo('instagram_url')): ?>
                        <a href="<?php echo escapeOutput(getCompanyInfo('instagram_url')); ?>" target="_blank" rel="noopener" aria-label="Instagram">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <?php endif; ?>
                        <?php if (getCompanyInfo('linkedin_url')): ?>
                        <a href="<?php echo escapeOutput(getCompanyInfo('linkedin_url')); ?>" target="_blank" rel="noopener" aria-label="LinkedIn">
                            <i class="fab fa-linkedin-in"></i>
                        </a>
                        <?php endif; ?>
                        <?php if (getCompanyInfo('youtube_url')): ?>
                        <a href="<?php echo escapeOutput(getCompanyInfo('youtube_url')); ?>" target="_blank" rel="noopener" aria-label="YouTube">
                            <i class="fab fa-youtube"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="footer-bottom">
                <div class="footer-bottom-content">
                    <p>&copy; <?php echo date('Y'); ?> <?php echo escapeOutput(getCompanyInfo('company_name')); ?>. All rights reserved.</p>
                    <div class="footer-bottom-links">
                        <a href="<?php echo SITE_URL; ?>/privacy-policy.php">Privacy Policy</a>
                        <a href="<?php echo SITE_URL; ?>/terms-of-service.php">Terms of Service</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="back-to-top" class="back-to-top" aria-label="Back to top">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
    
    <!-- Additional JavaScript for specific pages -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js_file): ?>
            <script src="<?php echo ASSETS_URL; ?>/js/<?php echo $js_file; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- Google Analytics -->
    <?php if (getSiteSetting('google_analytics_id')): ?>
    <script async src="https://www.googletagmanager.com/gtag/js?id=<?php echo escapeOutput(getSiteSetting('google_analytics_id')); ?>"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '<?php echo escapeOutput(getSiteSetting('google_analytics_id')); ?>');
    </script>
    <?php endif; ?>

    <style>
    .footer-contact {
        margin: 20px 0;
    }

    .footer-contact p {
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .footer-contact i {
        color: #fbbf24;
        width: 16px;
    }

    .footer-links {
        list-style: none;
        padding: 0;
    }

    .footer-links li {
        margin-bottom: 8px;
    }

    .footer-links a {
        color: #d1d5db;
        transition: color 0.3s ease;
    }

    .footer-links a:hover {
        color: #fbbf24;
    }

    .footer-contact-info {
        margin: 15px 0;
    }

    .footer-contact-info p {
        margin-bottom: 8px;
        font-size: 0.9rem;
    }

    .footer-social {
        display: flex;
        gap: 15px;
        margin-top: 20px;
    }

    .footer-social a {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: #374151;
        color: #d1d5db;
        border-radius: 50%;
        transition: all 0.3s ease;
    }

    .footer-social a:hover {
        background: #fbbf24;
        color: #1f2937;
        transform: translateY(-2px);
    }

    .footer-bottom {
        border-top: 1px solid #374151;
        padding-top: 20px;
        margin-top: 40px;
    }

    .footer-bottom-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-wrap: wrap;
        gap: 20px;
    }

    .footer-bottom-links {
        display: flex;
        gap: 20px;
    }

    .footer-bottom-links a {
        color: #9ca3af;
        font-size: 0.9rem;
        transition: color 0.3s ease;
    }

    .footer-bottom-links a:hover {
        color: #fbbf24;
    }

    .back-to-top {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 50px;
        height: 50px;
        background: #2563eb;
        color: white;
        border: none;
        border-radius: 50%;
        cursor: pointer;
        display: none;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        transition: all 0.3s ease;
        z-index: 1000;
        box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
    }

    .back-to-top:hover {
        background: #1d4ed8;
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(37, 99, 235, 0.4);
    }

    .back-to-top.show {
        display: flex;
    }

    @media (max-width: 768px) {
        .footer-content {
            grid-template-columns: 1fr;
            gap: 30px;
        }

        .footer-bottom-content {
            flex-direction: column;
            text-align: center;
        }

        .footer-social {
            justify-content: center;
        }

        .back-to-top {
            bottom: 20px;
            right: 20px;
            width: 45px;
            height: 45px;
        }
    }
    </style>

    <script>
    // Back to top button functionality
    document.addEventListener('DOMContentLoaded', function() {
        const backToTopBtn = document.getElementById('back-to-top');
        
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTopBtn.classList.add('show');
            } else {
                backToTopBtn.classList.remove('show');
            }
        });
        
        backToTopBtn.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    });
    </script>

</body>
</html>
