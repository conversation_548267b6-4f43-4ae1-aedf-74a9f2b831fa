<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? escapeOutput($page_title) : SITE_NAME; ?></title>
    <meta name="description" content="<?php echo isset($page_description) ? escapeOutput($page_description) : getSiteSetting('site_description'); ?>">
    <meta name="keywords" content="construction, civil engineering, groundworks, RC frames, basements, hard landscaping, London, building">
    <meta name="author" content="Flori Construction Ltd">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo isset($page_title) ? escapeOutput($page_title) : SITE_NAME; ?>">
    <meta property="og:description" content="<?php echo isset($page_description) ? escapeOutput($page_description) : getSiteSetting('site_description'); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL . $_SERVER['REQUEST_URI']; ?>">
    <meta property="og:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">
    
    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo isset($page_title) ? escapeOutput($page_title) : SITE_NAME; ?>">
    <meta name="twitter:description" content="<?php echo isset($page_description) ? escapeOutput($page_description) : getSiteSetting('site_description'); ?>">
    <meta name="twitter:image" content="<?php echo SITE_URL; ?>/assets/images/og-image.jpg">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    <link rel="apple-touch-icon" href="<?php echo ASSETS_URL; ?>/images/apple-touch-icon.png">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Main Stylesheet -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    
    <!-- Additional CSS for specific pages -->
    <?php if (isset($additional_css)): ?>
        <?php foreach ($additional_css as $css_file): ?>
            <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/<?php echo $css_file; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "ConstructionCompany",
        "name": "<?php echo escapeOutput(getCompanyInfo('company_name')); ?>",
        "description": "<?php echo escapeOutput(getSiteSetting('site_description')); ?>",
        "url": "<?php echo SITE_URL; ?>",
        "telephone": "<?php echo escapeOutput(getCompanyInfo('phone_primary')); ?>",
        "email": "<?php echo escapeOutput(getCompanyInfo('email_primary')); ?>",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "<?php echo escapeOutput(getCompanyInfo('address_full')); ?>",
            "addressLocality": "London",
            "addressCountry": "GB"
        },
        "sameAs": [
            "<?php echo escapeOutput(getCompanyInfo('facebook_url')); ?>",
            "<?php echo escapeOutput(getCompanyInfo('instagram_url')); ?>",
            "<?php echo escapeOutput(getCompanyInfo('linkedin_url')); ?>",
            "<?php echo escapeOutput(getCompanyInfo('youtube_url')); ?>"
        ],
        "serviceArea": {
            "@type": "GeoCircle",
            "geoMidpoint": {
                "@type": "GeoCoordinates",
                "latitude": "51.5074",
                "longitude": "-0.1278"
            },
            "geoRadius": "50000"
        },
        "hasOfferCatalog": {
            "@type": "OfferCatalog",
            "name": "Construction Services",
            "itemListElement": [
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Civil Engineering"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Groundworks"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "RC Frames Construction"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Basement Construction"
                    }
                },
                {
                    "@type": "Offer",
                    "itemOffered": {
                        "@type": "Service",
                        "name": "Hard Landscaping"
                    }
                }
            ]
        }
    }
    </script>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <!-- Top Header Bar -->
        <div class="header-top">
            <div class="container">
                <div class="contact-info">
                    <a href="tel:<?php echo escapeOutput(getCompanyInfo('phone_primary')); ?>">
                        <i class="fas fa-phone"></i> <?php echo escapeOutput(getCompanyInfo('phone_primary')); ?>
                    </a>
                    <a href="mailto:<?php echo escapeOutput(getCompanyInfo('email_primary')); ?>">
                        <i class="fas fa-envelope"></i> <?php echo escapeOutput(getCompanyInfo('email_primary')); ?>
                    </a>
                </div>
                <div class="social-links">
                    <?php if (getCompanyInfo('facebook_url')): ?>
                    <a href="<?php echo escapeOutput(getCompanyInfo('facebook_url')); ?>" target="_blank" rel="noopener">
                        <i class="fab fa-facebook-f"></i>
                    </a>
                    <?php endif; ?>
                    <?php if (getCompanyInfo('instagram_url')): ?>
                    <a href="<?php echo escapeOutput(getCompanyInfo('instagram_url')); ?>" target="_blank" rel="noopener">
                        <i class="fab fa-instagram"></i>
                    </a>
                    <?php endif; ?>
                    <?php if (getCompanyInfo('linkedin_url')): ?>
                    <a href="<?php echo escapeOutput(getCompanyInfo('linkedin_url')); ?>" target="_blank" rel="noopener">
                        <i class="fab fa-linkedin-in"></i>
                    </a>
                    <?php endif; ?>
                    <?php if (getCompanyInfo('youtube_url')): ?>
                    <a href="<?php echo escapeOutput(getCompanyInfo('youtube_url')); ?>" target="_blank" rel="noopener">
                        <i class="fab fa-youtube"></i>
                    </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- Main Navigation -->
        <nav class="navbar">
            <div class="container">
                <div class="logo">
                    <a href="<?php echo SITE_URL; ?>">
                        <img src="<?php echo ASSETS_URL; ?>/images/logo.png" alt="<?php echo escapeOutput(getCompanyInfo('company_name')); ?>">
                    </a>
                </div>
                
                <ul class="nav-menu">
                    <li><a href="<?php echo SITE_URL; ?>" class="<?php echo basename($_SERVER['PHP_SELF']) === 'index.php' ? 'active' : ''; ?>">Home</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/about.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'about.php' ? 'active' : ''; ?>">About Us</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/services.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'services.php' ? 'active' : ''; ?>">Our Services</a></li>
                    <li class="dropdown">
                        <a href="<?php echo SITE_URL; ?>/projects.php" class="<?php echo in_array(basename($_SERVER['PHP_SELF']), ['projects.php', 'project.php']) ? 'active' : ''; ?>">Our Projects</a>
                        <ul class="dropdown-menu">
                            <li><a href="<?php echo SITE_URL; ?>/projects.php?category=completed-projects">Completed Projects</a></li>
                            <li><a href="<?php echo SITE_URL; ?>/projects.php?category=ongoing-projects">Ongoing Projects</a></li>
                        </ul>
                    </li>
                    <li><a href="<?php echo SITE_URL; ?>/gallery.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'gallery.php' ? 'active' : ''; ?>">Gallery</a></li>
                    <li><a href="<?php echo SITE_URL; ?>/contact.php" class="<?php echo basename($_SERVER['PHP_SELF']) === 'contact.php' ? 'active' : ''; ?>">Contact Us</a></li>
                </ul>
                
                <button class="mobile-menu-toggle">
                    <i class="fas fa-bars"></i>
                </button>
            </div>
        </nav>
    </header>

    <!-- Flash Messages -->
    <?php 
    $flash_message = getFlashMessage();
    if ($flash_message): 
    ?>
    <div class="flash-message flash-<?php echo $flash_message['type']; ?>" style="
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 5px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        max-width: 400px;
        word-wrap: break-word;
        <?php 
        $colors = [
            'success' => '#10b981',
            'error' => '#ef4444',
            'warning' => '#f59e0b',
            'info' => '#3b82f6'
        ];
        echo 'background-color: ' . ($colors[$flash_message['type']] ?? $colors['info']) . ';';
        ?>
    ">
        <?php echo escapeOutput($flash_message['text']); ?>
        <button onclick="this.parentElement.remove()" style="
            background: none;
            border: none;
            color: white;
            float: right;
            margin-left: 10px;
            cursor: pointer;
            font-size: 16px;
        ">&times;</button>
    </div>
    <script>
        setTimeout(function() {
            const flashMessage = document.querySelector('.flash-message');
            if (flashMessage) {
                flashMessage.style.opacity = '0';
                flashMessage.style.transform = 'translateX(100%)';
                setTimeout(() => flashMessage.remove(), 300);
            }
        }, 5000);
    </script>
    <?php endif; ?>

    <!-- Main Content -->
    <main class="main-content" style="margin-top: 120px;">
