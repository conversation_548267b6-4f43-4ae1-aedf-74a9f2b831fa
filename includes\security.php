<?php
/**
 * Security Functions for Flori Construction Ltd Website
 * 
 * This file contains security-related functions for input validation,
 * CSRF protection, authentication, and other security measures.
 */

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Verify CSRF token
 */
function verifyCSRFToken($token) {
    return isset($_SESSION[CSRF_TOKEN_NAME]) && 
           hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
    return $data;
}

/**
 * Validate email address
 */
function validateEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Validate phone number (basic validation)
 */
function validatePhone($phone) {
    $phone = preg_replace('/[^0-9+\-\s\(\)]/', '', $phone);
    return strlen($phone) >= 10 && strlen($phone) <= 20;
}

/**
 * Validate password strength
 */
function validatePassword($password) {
    if (strlen($password) < PASSWORD_MIN_LENGTH) {
        return false;
    }
    
    // Check for at least one uppercase, one lowercase, and one number
    if (!preg_match('/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/', $password)) {
        return false;
    }
    
    return true;
}

/**
 * Hash password securely
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password against hash
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate secure random string
 */
function generateRandomString($length = 32) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Check if user is logged in as admin
 */
function isAdminLoggedIn() {
    return isset($_SESSION[ADMIN_SESSION_NAME]) && 
           isset($_SESSION['admin_id']) && 
           isset($_SESSION['admin_username']);
}

/**
 * Require admin login
 */
function requireAdminLogin() {
    if (!isAdminLoggedIn()) {
        header('Location: ' . ADMIN_URL . '/login.php');
        exit;
    }
}

/**
 * Login admin user
 */
function loginAdmin($user_data) {
    $_SESSION[ADMIN_SESSION_NAME] = true;
    $_SESSION['admin_id'] = $user_data['id'];
    $_SESSION['admin_username'] = $user_data['username'];
    $_SESSION['admin_email'] = $user_data['email'];
    $_SESSION['admin_role'] = $user_data['role'];
    $_SESSION['admin_full_name'] = $user_data['full_name'];
    
    // Update last login time
    $db = getDB();
    $db->execute(
        "UPDATE admin_users SET last_login = NOW() WHERE id = ?",
        [$user_data['id']]
    );
}

/**
 * Logout admin user
 */
function logoutAdmin() {
    unset($_SESSION[ADMIN_SESSION_NAME]);
    unset($_SESSION['admin_id']);
    unset($_SESSION['admin_username']);
    unset($_SESSION['admin_email']);
    unset($_SESSION['admin_role']);
    unset($_SESSION['admin_full_name']);
    
    session_destroy();
}

/**
 * Get current admin user data
 */
function getCurrentAdmin() {
    if (!isAdminLoggedIn()) {
        return null;
    }
    
    return [
        'id' => $_SESSION['admin_id'],
        'username' => $_SESSION['admin_username'],
        'email' => $_SESSION['admin_email'],
        'role' => $_SESSION['admin_role'],
        'full_name' => $_SESSION['admin_full_name']
    ];
}

/**
 * Check if current admin has specific role
 */
function hasAdminRole($role) {
    $admin = getCurrentAdmin();
    return $admin && $admin['role'] === $role;
}

/**
 * Validate file upload
 */
function validateFileUpload($file, $allowed_types = ALLOWED_IMAGE_TYPES) {
    $errors = [];
    
    // Check if file was uploaded
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        $errors[] = "No file was uploaded.";
        return $errors;
    }
    
    // Check for upload errors
    if ($file['error'] !== UPLOAD_ERR_OK) {
        $errors[] = "File upload error: " . $file['error'];
        return $errors;
    }
    
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        $errors[] = "File size exceeds maximum allowed size (" . formatBytes(MAX_FILE_SIZE) . ").";
    }
    
    // Check file type
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    if (!in_array($file_extension, $allowed_types)) {
        $errors[] = "File type not allowed. Allowed types: " . implode(', ', $allowed_types);
    }
    
    // Check if file is actually an image
    if (in_array($file_extension, ['jpg', 'jpeg', 'png', 'gif', 'webp'])) {
        $image_info = getimagesize($file['tmp_name']);
        if ($image_info === false) {
            $errors[] = "File is not a valid image.";
        }
    }
    
    return $errors;
}

/**
 * Prevent XSS attacks
 */
function escapeOutput($data) {
    if (is_array($data)) {
        return array_map('escapeOutput', $data);
    }
    return htmlspecialchars($data, ENT_QUOTES, 'UTF-8');
}

/**
 * Rate limiting for forms
 */
function checkRateLimit($identifier, $max_attempts = 5, $time_window = 300) {
    $key = 'rate_limit_' . md5($identifier);
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = ['count' => 0, 'first_attempt' => time()];
    }
    
    $rate_data = $_SESSION[$key];
    
    // Reset if time window has passed
    if (time() - $rate_data['first_attempt'] > $time_window) {
        $_SESSION[$key] = ['count' => 1, 'first_attempt' => time()];
        return true;
    }
    
    // Check if limit exceeded
    if ($rate_data['count'] >= $max_attempts) {
        return false;
    }
    
    // Increment counter
    $_SESSION[$key]['count']++;
    return true;
}

/**
 * Log security events
 */
function logSecurityEvent($event, $details = []) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'details' => $details
    ];
    
    error_log("SECURITY: " . json_encode($log_entry));
}
?>
