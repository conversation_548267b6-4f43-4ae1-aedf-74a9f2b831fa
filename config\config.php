<?php
/**
 * Main Configuration File for Flori Construction Ltd Website
 * 
 * This file contains global configuration settings, constants,
 * and initialization code for the website.
 */

// Start session if not already started
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Error reporting (set to 0 in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone setting
date_default_timezone_set('Europe/London');

// Site configuration constants
define('SITE_NAME', 'Flori Construction Ltd');
define('SITE_URL', 'http://localhost/floriconstruction');
define('ADMIN_URL', SITE_URL . '/admin');
define('ASSETS_URL', SITE_URL . '/assets');
define('UPLOADS_URL', SITE_URL . '/uploads');

// Directory paths
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');
define('ADMIN_PATH', ROOT_PATH . '/admin');

// Security settings
define('ADMIN_SESSION_NAME', 'flori_admin_session');
define('CSRF_TOKEN_NAME', 'csrf_token');
define('PASSWORD_MIN_LENGTH', 8);

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_IMAGE_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'webp']);
define('THUMBNAIL_WIDTH', 300);
define('THUMBNAIL_HEIGHT', 200);

// Pagination settings
define('PROJECTS_PER_PAGE', 12);
define('ADMIN_ITEMS_PER_PAGE', 20);

// Email settings
define('CONTACT_EMAIL', '<EMAIL>');
define('ADMIN_EMAIL', '<EMAIL>');
define('FROM_EMAIL', '<EMAIL>');

// Include required files
require_once 'database.php';
require_once INCLUDES_PATH . '/functions.php';
require_once INCLUDES_PATH . '/security.php';

/**
 * Autoloader for classes
 */
spl_autoload_register(function ($class) {
    $file = INCLUDES_PATH . '/classes/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});

/**
 * Global error handler
 */
function globalErrorHandler($errno, $errstr, $errfile, $errline) {
    $error_message = "Error: [$errno] $errstr in $errfile on line $errline";
    error_log($error_message);
    
    // Don't show errors to users in production
    if (defined('PRODUCTION') && PRODUCTION) {
        return true;
    }
    
    return false;
}

set_error_handler('globalErrorHandler');

/**
 * Global exception handler
 */
function globalExceptionHandler($exception) {
    $error_message = "Uncaught exception: " . $exception->getMessage() . 
                    " in " . $exception->getFile() . 
                    " on line " . $exception->getLine();
    error_log($error_message);
    
    // Show generic error page to users
    if (defined('PRODUCTION') && PRODUCTION) {
        include 'error.php';
        exit;
    }
}

set_exception_handler('globalExceptionHandler');

/**
 * Initialize application
 */
function initializeApp() {
    // Create upload directories if they don't exist
    $upload_dirs = [
        UPLOADS_PATH,
        UPLOADS_PATH . '/projects',
        UPLOADS_PATH . '/gallery',
        UPLOADS_PATH . '/services',
        UPLOADS_PATH . '/thumbnails'
    ];
    
    foreach ($upload_dirs as $dir) {
        if (!is_dir($dir)) {
            mkdir($dir, 0755, true);
        }
    }
    
    // Test database connection
    if (!testDatabaseConnection()) {
        throw new Exception("Database connection failed. Please check your configuration.");
    }
}

// Initialize the application
try {
    initializeApp();
} catch (Exception $e) {
    error_log("Application initialization failed: " . $e->getMessage());
    if (!defined('PRODUCTION') || !PRODUCTION) {
        die("Application initialization failed: " . $e->getMessage());
    }
}
?>
