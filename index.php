<?php
/**
 * Homepage for Flori Construction Ltd Website
 * 
 * This is the main landing page showcasing the company's services,
 * featured projects, and company information.
 */

require_once 'config/config.php';

// Get homepage content
$hero_title = getContentBlock('homepage_hero_title', 'Creating spaces that inspire');
$hero_subtitle = getContentBlock('homepage_hero_subtitle', 'Professional construction services with over 15 years of experience in London and surrounding areas');
$about_title = getContentBlock('homepage_about_title', 'About our company');
$about_content = getContentBlock('homepage_about_content', 'Flori Construction Ltd began its journey in the heart of London, founded on the principles of quality, professionalism, and an unwavering dedication to customer satisfaction.');

// Get featured services
$db = getDB();
$services = $db->fetchAll(
    "SELECT * FROM services WHERE is_active = 1 ORDER BY sort_order ASC LIMIT 5"
);

// Get featured projects
$featured_projects = $db->fetchAll(
    "SELECT p.*, pc.name as category_name 
     FROM projects p 
     LEFT JOIN project_categories pc ON p.category_id = pc.id 
     WHERE p.is_featured = 1 AND p.status = 'completed' 
     ORDER BY p.sort_order ASC LIMIT 6"
);

// Get testimonials
$testimonials = $db->fetchAll(
    "SELECT * FROM testimonials WHERE is_featured = 1 AND is_active = 1 ORDER BY sort_order ASC LIMIT 3"
);

$page_title = 'Home - ' . SITE_NAME;
$page_description = 'Leading construction company in London specializing in civil engineering, groundworks, RC frames, basements, and hard landscaping.';

include 'includes/header.php';
?>

<!-- Hero Section -->
<section class="hero" id="home">
    <div class="container">
        <div class="hero-content">
            <h1><?php echo escapeOutput($hero_title); ?></h1>
            <p><?php echo escapeOutput($hero_subtitle); ?></p>
            <div class="hero-buttons">
                <a href="services.php" class="btn btn-primary">Our Services</a>
                <a href="contact.php" class="btn btn-secondary">Get Quote</a>
            </div>
        </div>
    </div>
</section>

<!-- About Section -->
<section class="section about" id="about">
    <div class="container">
        <div class="about-content">
            <div class="about-text">
                <h2><?php echo escapeOutput($about_title); ?></h2>
                <p><?php echo escapeOutput($about_content); ?></p>
                <p>Our team brings together many years of collective experience in the construction industry, embodying extensive knowledge and refined processes. We specialize in:</p>
                <ul class="services-list">
                    <?php foreach ($services as $service): ?>
                    <li><i class="fas fa-check"></i> <?php echo escapeOutput($service['title']); ?></li>
                    <?php endforeach; ?>
                </ul>
                <a href="about.php" class="btn btn-outline">Learn More About Us</a>
            </div>
            <div class="about-image">
                <img src="assets/images/about-image.jpg" alt="Flori Construction Team" loading="lazy">
            </div>
        </div>
    </div>
</section>

<!-- Services Section -->
<section class="section services" id="services">
    <div class="container">
        <div class="section-header">
            <h2>Services We Provide</h2>
            <p>We are committed to delivering high-quality services in these areas and more. Our goal is to provide reliable and efficient solutions that meet the unique needs of each project.</p>
        </div>
        
        <div class="services-grid">
            <?php foreach ($services as $service): ?>
            <div class="service-card">
                <div class="service-icon">
                    <i class="<?php echo escapeOutput($service['icon_class'] ?: 'fas fa-tools'); ?>"></i>
                </div>
                <h3><?php echo escapeOutput($service['title']); ?></h3>
                <p><?php echo escapeOutput($service['short_description']); ?></p>
                <a href="services.php#<?php echo escapeOutput($service['slug']); ?>" class="btn btn-outline">Learn More</a>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<!-- Featured Projects Section -->
<section class="section projects" id="projects">
    <div class="container">
        <div class="section-header">
            <h2>Every Project is Unique and Custom Made</h2>
            <p>We are proud to showcase a selection of our completed projects that demonstrate our expertise and commitment to delivering high-quality construction solutions.</p>
        </div>
        
        <div class="projects-grid">
            <?php foreach ($featured_projects as $project): ?>
            <div class="project-card">
                <div class="project-image">
                    <?php if ($project['featured_image']): ?>
                    <img src="<?php echo escapeOutput($project['featured_image']); ?>" 
                         alt="<?php echo escapeOutput($project['title']); ?>" 
                         loading="lazy">
                    <?php else: ?>
                    <img src="assets/images/project-placeholder.jpg" 
                         alt="<?php echo escapeOutput($project['title']); ?>" 
                         loading="lazy">
                    <?php endif; ?>
                </div>
                <div class="project-content">
                    <div class="project-category"><?php echo escapeOutput($project['category_name'] ?: 'Project'); ?></div>
                    <h3><?php echo escapeOutput($project['title']); ?></h3>
                    <p><?php echo escapeOutput(truncateText($project['short_description'], 120)); ?></p>
                    <a href="project.php?slug=<?php echo escapeOutput($project['slug']); ?>" class="btn btn-outline">View Details</a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="projects.php" class="btn btn-primary">View All Projects</a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<?php if (!empty($testimonials)): ?>
<section class="section testimonials" style="background: #f9fafb;">
    <div class="container">
        <div class="section-header">
            <h2>What Our Clients Say</h2>
            <p>Don't just take our word for it - hear from our satisfied clients about their experience working with Flori Construction Ltd.</p>
        </div>
        
        <div class="testimonials-grid">
            <?php foreach ($testimonials as $testimonial): ?>
            <div class="testimonial-card">
                <div class="testimonial-content">
                    <div class="rating">
                        <?php for ($i = 1; $i <= 5; $i++): ?>
                        <i class="fas fa-star <?php echo $i <= $testimonial['rating'] ? 'active' : ''; ?>"></i>
                        <?php endfor; ?>
                    </div>
                    <p>"<?php echo escapeOutput($testimonial['testimonial_text']); ?>"</p>
                </div>
                <div class="testimonial-author">
                    <h4><?php echo escapeOutput($testimonial['client_name']); ?></h4>
                    <?php if ($testimonial['client_company']): ?>
                    <span><?php echo escapeOutput($testimonial['client_company']); ?></span>
                    <?php endif; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>
<?php endif; ?>

<!-- Call to Action Section -->
<section class="section cta" style="background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white;">
    <div class="container text-center">
        <h2>Ready to Start Your Project?</h2>
        <p>Get in touch with our team today for a free consultation and quote for your construction project.</p>
        <div class="cta-buttons">
            <a href="contact.php" class="btn btn-secondary">Get Free Quote</a>
            <a href="tel:<?php echo escapeOutput(getCompanyInfo('phone_primary')); ?>" class="btn btn-outline" style="border-color: white; color: white;">
                Call <?php echo escapeOutput(getCompanyInfo('phone_primary')); ?>
            </a>
        </div>
    </div>
</section>

<style>
.services-list {
    list-style: none;
    margin: 20px 0;
}

.services-list li {
    padding: 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.services-list i {
    color: #2563eb;
    font-size: 0.9rem;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.testimonial-card {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.rating {
    margin-bottom: 15px;
}

.rating .fa-star {
    color: #d1d5db;
    margin-right: 2px;
}

.rating .fa-star.active {
    color: #fbbf24;
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 20px;
    line-height: 1.6;
}

.testimonial-author h4 {
    margin-bottom: 5px;
    color: #1f2937;
}

.testimonial-author span {
    color: #6b7280;
    font-size: 0.9rem;
}

.cta {
    text-align: center;
}

.cta h2 {
    margin-bottom: 15px;
}

.cta p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .hero-content h1 {
        font-size: 2.5rem;
    }
    
    .cta-buttons,
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-buttons .btn,
    .hero-buttons .btn {
        width: 200px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
