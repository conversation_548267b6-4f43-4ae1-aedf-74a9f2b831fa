<?php
/**
 * Core Functionality Tester for Flori Construction Ltd Website
 * 
 * This script systematically tests all core functionality
 */

// Start output buffering for clean display
ob_start();

// Include necessary files
$config_file = '';
if (file_exists('config/database_config.php')) {
    $config_file = 'config/database_config.php';
} elseif (file_exists('config/database.php')) {
    $config_file = 'config/database.php';
}

if ($config_file) {
    require_once $config_file;
}

if (file_exists('includes/functions.php')) {
    require_once 'includes/functions.php';
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Core Functionality Test - Flori Construction</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .test-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .test-item { padding: 10px; border-left: 4px solid #007bff; background: #f8f9fa; }
        .test-item.success { border-left-color: #28a745; }
        .test-item.error { border-left-color: #dc3545; }
        .test-item.warning { border-left-color: #ffc107; }
        h1, h2, h3 { color: #333; }
        .status-icon { font-size: 1.2em; margin-right: 8px; }
        .action-buttons { margin: 20px 0; }
        .btn { padding: 10px 20px; margin: 5px; text-decoration: none; border-radius: 5px; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-warning { background: #ffc107; color: black; }
        .btn-danger { background: #dc3545; color: white; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Core Functionality Test Results</h1>
        <p><strong>Test Date:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <?php
        $tests_passed = 0;
        $tests_failed = 0;
        $tests_warning = 0;
        
        // Test 1: Database Connection
        echo '<div class="test-section">';
        echo '<h2>1. Database Connection Test</h2>';
        
        try {
            if (!$config_file) {
                throw new Exception("No database configuration file found");
            }
            
            $dsn = "mysql:host=" . DB_HOST . ";charset=" . (defined('DB_CHARSET') ? DB_CHARSET : 'utf8mb4');
            $pdo = new PDO($dsn, DB_USER, DB_PASS, [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
            ]);
            
            echo '<div class="test-item success">';
            echo '<span class="status-icon">✅</span><strong>MySQL Connection:</strong> Successful';
            echo '</div>';
            $tests_passed++;
            
            // Check if database exists
            $stmt = $pdo->query("SHOW DATABASES LIKE '" . DB_NAME . "'");
            $db_exists = $stmt->rowCount() > 0;
            
            if ($db_exists) {
                echo '<div class="test-item success">';
                echo '<span class="status-icon">✅</span><strong>Database Exists:</strong> ' . DB_NAME;
                echo '</div>';
                $tests_passed++;
                
                // Connect to specific database
                $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . (defined('DB_CHARSET') ? DB_CHARSET : 'utf8mb4');
                $pdo = new PDO($dsn, DB_USER, DB_PASS, [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION
                ]);
                
                echo '<div class="test-item success">';
                echo '<span class="status-icon">✅</span><strong>Database Connection:</strong> Connected to ' . DB_NAME;
                echo '</div>';
                $tests_passed++;
                
            } else {
                echo '<div class="test-item error">';
                echo '<span class="status-icon">❌</span><strong>Database Missing:</strong> ' . DB_NAME . ' does not exist';
                echo '</div>';
                $tests_failed++;
            }
            
        } catch (Exception $e) {
            echo '<div class="test-item error">';
            echo '<span class="status-icon">❌</span><strong>Database Error:</strong> ' . $e->getMessage();
            echo '</div>';
            $tests_failed++;
        }
        echo '</div>';
        
        // Test 2: Database Tables
        echo '<div class="test-section">';
        echo '<h2>2. Database Tables Test</h2>';
        
        if (isset($pdo) && $db_exists) {
            try {
                $stmt = $pdo->query("SHOW TABLES");
                $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
                
                $required_tables = ['admin_users', 'services', 'projects', 'inquiries', 'gallery'];
                
                if (empty($tables)) {
                    echo '<div class="test-item error">';
                    echo '<span class="status-icon">❌</span><strong>No Tables:</strong> Database is empty';
                    echo '</div>';
                    $tests_failed++;
                } else {
                    echo '<div class="test-grid">';
                    foreach ($required_tables as $table) {
                        if (in_array($table, $tables)) {
                            // Check table has data
                            $stmt = $pdo->query("SELECT COUNT(*) as count FROM $table");
                            $count = $stmt->fetch()['count'];
                            
                            echo '<div class="test-item success">';
                            echo '<span class="status-icon">✅</span><strong>' . ucfirst($table) . ':</strong> ' . $count . ' records';
                            echo '</div>';
                            $tests_passed++;
                        } else {
                            echo '<div class="test-item error">';
                            echo '<span class="status-icon">❌</span><strong>' . ucfirst($table) . ':</strong> Table missing';
                            echo '</div>';
                            $tests_failed++;
                        }
                    }
                    echo '</div>';
                }
                
            } catch (Exception $e) {
                echo '<div class="test-item error">';
                echo '<span class="status-icon">❌</span><strong>Table Check Error:</strong> ' . $e->getMessage();
                echo '</div>';
                $tests_failed++;
            }
        } else {
            echo '<div class="test-item warning">';
            echo '<span class="status-icon">⚠️</span><strong>Skipped:</strong> Database connection required';
            echo '</div>';
            $tests_warning++;
        }
        echo '</div>';
        
        // Test 3: File Structure
        echo '<div class="test-section">';
        echo '<h2>3. File Structure Test</h2>';
        
        $required_files = [
            'index.php' => 'Homepage',
            'services.php' => 'Services Page',
            'contact.php' => 'Contact Page',
            'projects.php' => 'Projects Page',
            'admin/login.php' => 'Admin Login',
            'admin/dashboard.php' => 'Admin Dashboard',
            'includes/header.php' => 'Header Include',
            'includes/footer.php' => 'Footer Include',
            'includes/functions.php' => 'Functions',
            'assets/css' => 'CSS Directory',
            'assets/js' => 'JavaScript Directory',
            'uploads' => 'Uploads Directory'
        ];
        
        echo '<div class="test-grid">';
        foreach ($required_files as $file => $description) {
            if (file_exists($file)) {
                echo '<div class="test-item success">';
                echo '<span class="status-icon">✅</span><strong>' . $description . ':</strong> Found';
                echo '</div>';
                $tests_passed++;
            } else {
                echo '<div class="test-item error">';
                echo '<span class="status-icon">❌</span><strong>' . $description . ':</strong> Missing';
                echo '</div>';
                $tests_failed++;
            }
        }
        echo '</div>';
        echo '</div>';
        
        // Test 4: Directory Permissions
        echo '<div class="test-section">';
        echo '<h2>4. Directory Permissions Test</h2>';
        
        $upload_dirs = ['uploads', 'uploads/projects', 'uploads/gallery', 'uploads/services', 'uploads/thumbnails'];
        
        echo '<div class="test-grid">';
        foreach ($upload_dirs as $dir) {
            if (is_dir($dir)) {
                if (is_writable($dir)) {
                    echo '<div class="test-item success">';
                    echo '<span class="status-icon">✅</span><strong>' . $dir . ':</strong> Writable';
                    echo '</div>';
                    $tests_passed++;
                } else {
                    echo '<div class="test-item error">';
                    echo '<span class="status-icon">❌</span><strong>' . $dir . ':</strong> Not writable';
                    echo '</div>';
                    $tests_failed++;
                }
            } else {
                echo '<div class="test-item warning">';
                echo '<span class="status-icon">⚠️</span><strong>' . $dir . ':</strong> Directory missing';
                echo '</div>';
                $tests_warning++;
            }
        }
        echo '</div>';
        echo '</div>';
        ?>
        
        <!-- Test Summary -->
        <div class="test-section">
            <h2>📊 Test Summary</h2>
            <div class="test-grid">
                <div class="test-item success">
                    <span class="status-icon">✅</span><strong>Passed:</strong> <?php echo $tests_passed; ?> tests
                </div>
                <div class="test-item error">
                    <span class="status-icon">❌</span><strong>Failed:</strong> <?php echo $tests_failed; ?> tests
                </div>
                <div class="test-item warning">
                    <span class="status-icon">⚠️</span><strong>Warnings:</strong> <?php echo $tests_warning; ?> tests
                </div>
                <div class="test-item info">
                    <span class="status-icon">📈</span><strong>Total:</strong> <?php echo $tests_passed + $tests_failed + $tests_warning; ?> tests
                </div>
            </div>
        </div>
        
        <!-- Action Buttons -->
        <div class="action-buttons">
            <h3>🚀 Quick Actions</h3>
            <a href="setup.php" class="btn btn-primary">Run Setup Wizard</a>
            <a href="index.php" class="btn btn-success">View Website</a>
            <a href="admin/login.php" class="btn btn-warning">Admin Login</a>
            <a href="check_database.php" class="btn btn-info">Database Checker</a>
            <a href="test_functionality.php" class="btn btn-secondary">Refresh Tests</a>
        </div>
        
        <?php if ($tests_failed > 0): ?>
        <div class="test-section" style="border-color: #dc3545;">
            <h3 style="color: #dc3545;">🔧 Recommended Actions</h3>
            <ul>
                <?php if ($tests_failed > 0): ?>
                <li>Run the <strong>Setup Wizard</strong> to create database and tables</li>
                <li>Check file permissions for upload directories</li>
                <li>Verify all required files are present</li>
                <?php endif; ?>
            </ul>
        </div>
        <?php endif; ?>
        
    </div>
</body>
</html>
