        </main>
    </div>

    <!-- JavaScript -->
    <script src="<?php echo ASSETS_URL; ?>/js/admin.js"></script>
    
    <!-- Additional JavaScript for specific pages -->
    <?php if (isset($additional_js)): ?>
        <?php foreach ($additional_js as $js_file): ?>
            <script src="<?php echo ASSETS_URL; ?>/js/<?php echo $js_file; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <script>
        // Global admin JavaScript functions
        
        /**
         * Confirm delete action
         */
        function confirmDelete(message = 'Are you sure you want to delete this item?') {
            return confirm(message);
        }
        
        /**
         * Show loading state on form submission
         */
        function showFormLoading(form) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
            }
        }
        
        /**
         * Auto-resize textareas
         */
        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            textarea.style.height = textarea.scrollHeight + 'px';
        }
        
        /**
         * Initialize auto-resize for all textareas
         */
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    autoResizeTextarea(this);
                });
                // Initial resize
                autoResizeTextarea(textarea);
            });
            
            // Add loading state to forms
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    showFormLoading(this);
                });
            });
        });
        
        /**
         * Image preview functionality
         */
        function previewImage(input, previewElement) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    previewElement.src = e.target.result;
                    previewElement.style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }
        
        /**
         * Copy to clipboard functionality
         */
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                showNotification('Copied to clipboard!', 'success');
            }).catch(function() {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showNotification('Copied to clipboard!', 'success');
            });
        }
        
        /**
         * Show notification
         */
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.admin-notification');
            existingNotifications.forEach(notification => notification.remove());
            
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `admin-notification admin-notification-${type}`;
            notification.innerHTML = `
                <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'}"></i>
                <span>${message}</span>
                <button onclick="this.parentElement.remove()" class="notification-close">&times;</button>
            `;
            
            // Add styles
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 15px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                display: flex;
                align-items: center;
                gap: 10px;
                max-width: 400px;
                word-wrap: break-word;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                ${type === 'success' ? 'background: #10b981;' : 
                  type === 'error' ? 'background: #ef4444;' : 
                  type === 'warning' ? 'background: #f59e0b;' : 
                  'background: #3b82f6;'}
            `;
            
            // Style close button
            const closeBtn = notification.querySelector('.notification-close');
            closeBtn.style.cssText = `
                background: none;
                border: none;
                color: white;
                font-size: 18px;
                cursor: pointer;
                margin-left: auto;
                opacity: 0.8;
            `;
            
            // Add to page
            document.body.appendChild(notification);
            
            // Animate in
            setTimeout(() => {
                notification.style.transform = 'translateX(0)';
            }, 10);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 300);
            }, 5000);
        }
        
        /**
         * AJAX form submission
         */
        function submitFormAjax(form, successCallback, errorCallback) {
            const formData = new FormData(form);
            
            fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (successCallback) {
                        successCallback(data);
                    } else {
                        showNotification(data.message || 'Operation completed successfully!', 'success');
                    }
                } else {
                    if (errorCallback) {
                        errorCallback(data);
                    } else {
                        showNotification(data.message || 'An error occurred.', 'error');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (errorCallback) {
                    errorCallback({message: 'Network error occurred.'});
                } else {
                    showNotification('Network error occurred.', 'error');
                }
            });
        }
        
        /**
         * Table sorting functionality
         */
        function sortTable(table, columnIndex, direction = 'asc') {
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));
            
            rows.sort((a, b) => {
                const aValue = a.cells[columnIndex].textContent.trim();
                const bValue = b.cells[columnIndex].textContent.trim();
                
                // Try to parse as numbers
                const aNum = parseFloat(aValue);
                const bNum = parseFloat(bValue);
                
                if (!isNaN(aNum) && !isNaN(bNum)) {
                    return direction === 'asc' ? aNum - bNum : bNum - aNum;
                }
                
                // String comparison
                return direction === 'asc' 
                    ? aValue.localeCompare(bValue)
                    : bValue.localeCompare(aValue);
            });
            
            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
        }
        
        /**
         * Search functionality
         */
        function searchTable(searchInput, table) {
            const searchTerm = searchInput.value.toLowerCase();
            const rows = table.querySelectorAll('tbody tr');
            
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchTerm) ? '' : 'none';
            });
        }
        
        /**
         * Bulk actions functionality
         */
        function handleBulkAction(action, selectedItems) {
            if (selectedItems.length === 0) {
                showNotification('Please select at least one item.', 'warning');
                return;
            }
            
            const confirmMessage = `Are you sure you want to ${action} ${selectedItems.length} item(s)?`;
            if (!confirm(confirmMessage)) {
                return;
            }
            
            // Implement bulk action logic here
            console.log(`Bulk ${action} for items:`, selectedItems);
        }
        
        /**
         * Initialize bulk selection
         */
        document.addEventListener('DOMContentLoaded', function() {
            const selectAllCheckbox = document.querySelector('#select-all');
            const itemCheckboxes = document.querySelectorAll('.item-checkbox');
            
            if (selectAllCheckbox && itemCheckboxes.length > 0) {
                selectAllCheckbox.addEventListener('change', function() {
                    itemCheckboxes.forEach(checkbox => {
                        checkbox.checked = this.checked;
                    });
                });
                
                itemCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const checkedCount = document.querySelectorAll('.item-checkbox:checked').length;
                        selectAllCheckbox.checked = checkedCount === itemCheckboxes.length;
                        selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < itemCheckboxes.length;
                    });
                });
            }
        });
        
        /**
         * Auto-save functionality
         */
        let autoSaveTimeout;
        function enableAutoSave(form, interval = 30000) { // 30 seconds
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                input.addEventListener('input', function() {
                    clearTimeout(autoSaveTimeout);
                    autoSaveTimeout = setTimeout(() => {
                        saveFormData(form);
                    }, interval);
                });
            });
        }
        
        function saveFormData(form) {
            const formData = new FormData(form);
            formData.append('auto_save', '1');
            
            fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Auto-saved', 'success');
                }
            })
            .catch(error => {
                console.error('Auto-save failed:', error);
            });
        }
    </script>

    <style>
        .admin-notification {
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .notification-close:hover {
            opacity: 1 !important;
        }
        
        /* Loading spinner */
        .fa-spinner {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Table enhancements */
        .sortable-header {
            cursor: pointer;
            user-select: none;
        }
        
        .sortable-header:hover {
            background-color: #f3f4f6;
        }
        
        .sort-indicator {
            margin-left: 5px;
            opacity: 0.5;
        }
        
        .sort-indicator.active {
            opacity: 1;
        }
        
        /* Bulk selection */
        .bulk-actions {
            margin-bottom: 20px;
            padding: 15px;
            background: #f8fafc;
            border-radius: 6px;
            display: none;
        }
        
        .bulk-actions.show {
            display: block;
        }
    </style>

</body>
</html>
