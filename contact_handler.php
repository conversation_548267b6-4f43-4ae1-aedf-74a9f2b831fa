<?php
/**
 * Contact Form Handler for Flori Construction Ltd Website
 * 
 * This script processes contact form submissions, validates data,
 * stores inquiries in the database, and sends email notifications.
 */

require_once 'config/config.php';

// Set content type for JSON response
header('Content-Type: application/json');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
        throw new Exception('Invalid security token. Please refresh the page and try again.');
    }
    
    // Rate limiting
    $client_ip = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    if (!checkRateLimit($client_ip, 5, 300)) { // 5 attempts per 5 minutes
        throw new Exception('Too many requests. Please wait a few minutes before trying again.');
    }
    
    // Sanitize and validate input data
    $name = sanitizeInput($_POST['name'] ?? '');
    $email = sanitizeInput($_POST['email'] ?? '');
    $phone = sanitizeInput($_POST['phone'] ?? '');
    $company = sanitizeInput($_POST['company'] ?? '');
    $service_interest = sanitizeInput($_POST['service_interest'] ?? '');
    $subject = sanitizeInput($_POST['subject'] ?? '');
    $message = sanitizeInput($_POST['message'] ?? '');
    
    // Validation
    $errors = [];
    
    if (empty($name)) {
        $errors[] = 'Full name is required.';
    } elseif (strlen($name) < 2) {
        $errors[] = 'Full name must be at least 2 characters long.';
    }
    
    if (empty($email)) {
        $errors[] = 'Email address is required.';
    } elseif (!validateEmail($email)) {
        $errors[] = 'Please enter a valid email address.';
    }
    
    if (!empty($phone) && !validatePhone($phone)) {
        $errors[] = 'Please enter a valid phone number.';
    }
    
    if (empty($message)) {
        $errors[] = 'Message is required.';
    } elseif (strlen($message) < 10) {
        $errors[] = 'Message must be at least 10 characters long.';
    }
    
    // Check for spam patterns
    $spam_patterns = [
        '/\b(viagra|cialis|casino|poker|loan|mortgage|insurance|seo|marketing)\b/i',
        '/\b(click here|visit now|buy now|act now)\b/i',
        '/http[s]?:\/\/[^\s]+/i' // URLs in message
    ];
    
    foreach ($spam_patterns as $pattern) {
        if (preg_match($pattern, $message) || preg_match($pattern, $subject)) {
            $errors[] = 'Your message appears to be spam. Please contact us directly.';
            break;
        }
    }
    
    if (!empty($errors)) {
        echo json_encode(['success' => false, 'message' => implode(' ', $errors)]);
        exit;
    }
    
    // Get client information
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $ip_address = $client_ip;
    
    // Insert inquiry into database
    $db = getDB();
    
    $inquiry_id = $db->execute(
        "INSERT INTO contact_inquiries (name, email, phone, company, subject, message, service_interest, ip_address, user_agent, status) 
         VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'new')",
        [$name, $email, $phone, $company, $subject, $message, $service_interest, $ip_address, $user_agent]
    );
    
    $inquiry_id = $db->lastInsertId();
    
    // Prepare email content
    $email_subject = 'New Contact Inquiry - ' . SITE_NAME;
    if (!empty($subject)) {
        $email_subject .= ' - ' . $subject;
    }
    
    $email_body = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .field { margin-bottom: 15px; }
            .label { font-weight: bold; color: #2563eb; }
            .footer { background: #f8f9fa; padding: 15px; text-align: center; font-size: 0.9rem; color: #666; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h2>New Contact Inquiry</h2>
        </div>
        <div class='content'>
            <div class='field'>
                <span class='label'>Inquiry ID:</span> #" . $inquiry_id . "
            </div>
            <div class='field'>
                <span class='label'>Name:</span> " . escapeOutput($name) . "
            </div>
            <div class='field'>
                <span class='label'>Email:</span> " . escapeOutput($email) . "
            </div>";
    
    if (!empty($phone)) {
        $email_body .= "
            <div class='field'>
                <span class='label'>Phone:</span> " . escapeOutput($phone) . "
            </div>";
    }
    
    if (!empty($company)) {
        $email_body .= "
            <div class='field'>
                <span class='label'>Company:</span> " . escapeOutput($company) . "
            </div>";
    }
    
    if (!empty($service_interest)) {
        $email_body .= "
            <div class='field'>
                <span class='label'>Service of Interest:</span> " . escapeOutput($service_interest) . "
            </div>";
    }
    
    if (!empty($subject)) {
        $email_body .= "
            <div class='field'>
                <span class='label'>Subject:</span> " . escapeOutput($subject) . "
            </div>";
    }
    
    $email_body .= "
            <div class='field'>
                <span class='label'>Message:</span><br>
                " . nl2br(escapeOutput($message)) . "
            </div>
            <div class='field'>
                <span class='label'>Submitted:</span> " . date('F j, Y \a\t g:i A') . "
            </div>
            <div class='field'>
                <span class='label'>IP Address:</span> " . escapeOutput($ip_address) . "
            </div>
        </div>
        <div class='footer'>
            <p>This inquiry was submitted through the contact form on " . SITE_NAME . "</p>
            <p>Please respond to the client at: " . escapeOutput($email) . "</p>
        </div>
    </body>
    </html>";
    
    // Send email to admin
    $admin_email = getCompanyInfo('email_primary', ADMIN_EMAIL);
    $email_sent = sendEmail($admin_email, $email_subject, $email_body, FROM_EMAIL);
    
    // Send auto-reply to client
    $auto_reply_subject = 'Thank you for contacting ' . getCompanyInfo('company_name');
    $auto_reply_body = "
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .header { background: #2563eb; color: white; padding: 20px; text-align: center; }
            .content { padding: 20px; }
            .footer { background: #f8f9fa; padding: 15px; text-align: center; font-size: 0.9rem; color: #666; }
        </style>
    </head>
    <body>
        <div class='header'>
            <h2>Thank You for Your Inquiry</h2>
        </div>
        <div class='content'>
            <p>Dear " . escapeOutput($name) . ",</p>
            
            <p>Thank you for contacting " . escapeOutput(getCompanyInfo('company_name')) . ". We have received your inquiry and will respond within 24 hours.</p>
            
            <p><strong>Your inquiry details:</strong></p>
            <ul>
                <li><strong>Inquiry ID:</strong> #" . $inquiry_id . "</li>
                <li><strong>Service of Interest:</strong> " . escapeOutput($service_interest ?: 'General Inquiry') . "</li>
                <li><strong>Submitted:</strong> " . date('F j, Y \a\t g:i A') . "</li>
            </ul>
            
            <p>If you need immediate assistance, please call us at " . escapeOutput(getCompanyInfo('phone_primary')) . ".</p>
            
            <p>Best regards,<br>
            The " . escapeOutput(getCompanyInfo('company_name')) . " Team</p>
        </div>
        <div class='footer'>
            <p>" . escapeOutput(getCompanyInfo('company_name')) . "</p>
            <p>" . escapeOutput(getCompanyInfo('address_full')) . "</p>
            <p>Phone: " . escapeOutput(getCompanyInfo('phone_primary')) . " | Email: " . escapeOutput(getCompanyInfo('email_primary')) . "</p>
        </div>
    </body>
    </html>";
    
    sendEmail($email, $auto_reply_subject, $auto_reply_body, FROM_EMAIL);
    
    // Log the inquiry
    error_log("New contact inquiry #$inquiry_id from $name ($email)");
    
    // Return success response
    echo json_encode([
        'success' => true,
        'message' => 'Thank you for your message! We will get back to you within 24 hours.',
        'inquiry_id' => $inquiry_id
    ]);
    
} catch (Exception $e) {
    // Log the error
    error_log("Contact form error: " . $e->getMessage());
    
    // Log security event if it's a security-related error
    if (strpos($e->getMessage(), 'security') !== false || strpos($e->getMessage(), 'spam') !== false) {
        logSecurityEvent('contact_form_security_violation', [
            'message' => $e->getMessage(),
            'post_data' => $_POST
        ]);
    }
    
    // Return error response
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
