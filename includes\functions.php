<?php
/**
 * General Functions for Flori Construction Ltd Website
 * 
 * This file contains utility functions used throughout the website
 * for common operations like formatting, file handling, etc.
 */

/**
 * Format file size in human readable format
 */
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * Generate URL-friendly slug from string
 */
function generateSlug($string) {
    $slug = strtolower(trim($string));
    $slug = preg_replace('/[^a-z0-9-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    $slug = trim($slug, '-');
    return $slug;
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'F j, Y') {
    if (empty($date) || $date === '0000-00-00') {
        return '';
    }
    return date($format, strtotime($date));
}

/**
 * Format currency
 */
function formatCurrency($amount, $currency = '£') {
    return $currency . number_format($amount, 2);
}

/**
 * Truncate text to specified length
 */
function truncateText($text, $length = 150, $suffix = '...') {
    if (strlen($text) <= $length) {
        return $text;
    }
    
    return substr($text, 0, $length) . $suffix;
}

/**
 * Get file extension from filename
 */
function getFileExtension($filename) {
    return strtolower(pathinfo($filename, PATHINFO_EXTENSION));
}

/**
 * Generate unique filename
 */
function generateUniqueFilename($original_filename) {
    $extension = getFileExtension($original_filename);
    $basename = pathinfo($original_filename, PATHINFO_FILENAME);
    $basename = generateSlug($basename);
    
    return $basename . '_' . time() . '_' . rand(1000, 9999) . '.' . $extension;
}

/**
 * Upload file to specified directory
 */
function uploadFile($file, $upload_dir, $allowed_types = ALLOWED_IMAGE_TYPES) {
    // Validate file
    $errors = validateFileUpload($file, $allowed_types);
    if (!empty($errors)) {
        return ['success' => false, 'errors' => $errors];
    }
    
    // Create upload directory if it doesn't exist
    if (!is_dir($upload_dir)) {
        mkdir($upload_dir, 0755, true);
    }
    
    // Generate unique filename
    $filename = generateUniqueFilename($file['name']);
    $filepath = $upload_dir . '/' . $filename;
    
    // Move uploaded file
    if (move_uploaded_file($file['tmp_name'], $filepath)) {
        return [
            'success' => true,
            'filename' => $filename,
            'filepath' => $filepath,
            'url' => str_replace(ROOT_PATH, SITE_URL, $filepath)
        ];
    } else {
        return ['success' => false, 'errors' => ['Failed to move uploaded file.']];
    }
}

/**
 * Create thumbnail image
 */
function createThumbnail($source_path, $thumbnail_path, $width = THUMBNAIL_WIDTH, $height = THUMBNAIL_HEIGHT) {
    $image_info = getimagesize($source_path);
    if ($image_info === false) {
        return false;
    }
    
    $source_width = $image_info[0];
    $source_height = $image_info[1];
    $mime_type = $image_info['mime'];
    
    // Create source image resource
    switch ($mime_type) {
        case 'image/jpeg':
            $source_image = imagecreatefromjpeg($source_path);
            break;
        case 'image/png':
            $source_image = imagecreatefrompng($source_path);
            break;
        case 'image/gif':
            $source_image = imagecreatefromgif($source_path);
            break;
        default:
            return false;
    }
    
    if (!$source_image) {
        return false;
    }
    
    // Calculate thumbnail dimensions
    $aspect_ratio = $source_width / $source_height;
    
    if ($width / $height > $aspect_ratio) {
        $thumb_width = $height * $aspect_ratio;
        $thumb_height = $height;
    } else {
        $thumb_width = $width;
        $thumb_height = $width / $aspect_ratio;
    }
    
    // Create thumbnail image
    $thumbnail = imagecreatetruecolor($thumb_width, $thumb_height);
    
    // Preserve transparency for PNG and GIF
    if ($mime_type === 'image/png' || $mime_type === 'image/gif') {
        imagealphablending($thumbnail, false);
        imagesavealpha($thumbnail, true);
        $transparent = imagecolorallocatealpha($thumbnail, 255, 255, 255, 127);
        imagefill($thumbnail, 0, 0, $transparent);
    }
    
    // Resize image
    imagecopyresampled(
        $thumbnail, $source_image,
        0, 0, 0, 0,
        $thumb_width, $thumb_height,
        $source_width, $source_height
    );
    
    // Create thumbnail directory if it doesn't exist
    $thumbnail_dir = dirname($thumbnail_path);
    if (!is_dir($thumbnail_dir)) {
        mkdir($thumbnail_dir, 0755, true);
    }
    
    // Save thumbnail
    $result = false;
    switch ($mime_type) {
        case 'image/jpeg':
            $result = imagejpeg($thumbnail, $thumbnail_path, 85);
            break;
        case 'image/png':
            $result = imagepng($thumbnail, $thumbnail_path, 8);
            break;
        case 'image/gif':
            $result = imagegif($thumbnail, $thumbnail_path);
            break;
    }
    
    // Clean up memory
    imagedestroy($source_image);
    imagedestroy($thumbnail);
    
    return $result;
}

/**
 * Delete file and its thumbnail
 */
function deleteFile($filepath) {
    $deleted = false;
    
    // Delete main file
    if (file_exists($filepath)) {
        $deleted = unlink($filepath);
    }
    
    // Delete thumbnail if exists
    $filename = basename($filepath);
    $thumbnail_path = UPLOADS_PATH . '/thumbnails/' . $filename;
    if (file_exists($thumbnail_path)) {
        unlink($thumbnail_path);
    }
    
    return $deleted;
}

/**
 * Get pagination data
 */
function getPagination($current_page, $total_items, $items_per_page) {
    $total_pages = ceil($total_items / $items_per_page);
    $current_page = max(1, min($current_page, $total_pages));
    $offset = ($current_page - 1) * $items_per_page;
    
    return [
        'current_page' => $current_page,
        'total_pages' => $total_pages,
        'total_items' => $total_items,
        'items_per_page' => $items_per_page,
        'offset' => $offset,
        'has_previous' => $current_page > 1,
        'has_next' => $current_page < $total_pages,
        'previous_page' => $current_page - 1,
        'next_page' => $current_page + 1
    ];
}

/**
 * Send email (basic implementation)
 */
function sendEmail($to, $subject, $message, $from = FROM_EMAIL) {
    $headers = [
        'From: ' . $from,
        'Reply-To: ' . $from,
        'Content-Type: text/html; charset=UTF-8',
        'MIME-Version: 1.0'
    ];
    
    return mail($to, $subject, $message, implode("\r\n", $headers));
}

/**
 * Get content block by key
 */
function getContentBlock($key, $default = '') {
    static $content_cache = [];
    
    if (isset($content_cache[$key])) {
        return $content_cache[$key];
    }
    
    $db = getDB();
    $content = $db->fetchOne(
        "SELECT content FROM content_blocks WHERE block_key = ? AND is_active = 1",
        [$key]
    );
    
    $result = $content ? $content['content'] : $default;
    $content_cache[$key] = $result;
    
    return $result;
}

/**
 * Get company information
 */
function getCompanyInfo($key, $default = '') {
    static $info_cache = [];
    
    if (isset($info_cache[$key])) {
        return $info_cache[$key];
    }
    
    $db = getDB();
    $info = $db->fetchOne(
        "SELECT info_value FROM company_info WHERE info_key = ? AND is_public = 1",
        [$key]
    );
    
    $result = $info ? $info['info_value'] : $default;
    $info_cache[$key] = $result;
    
    return $result;
}

/**
 * Get site setting
 */
function getSiteSetting($key, $default = '') {
    static $settings_cache = [];
    
    if (isset($settings_cache[$key])) {
        return $settings_cache[$key];
    }
    
    $db = getDB();
    $setting = $db->fetchOne(
        "SELECT setting_value FROM site_settings WHERE setting_key = ?",
        [$key]
    );
    
    $result = $setting ? $setting['setting_value'] : $default;
    $settings_cache[$key] = $result;
    
    return $result;
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header('Location: ' . $url);
    exit;
}

/**
 * Get and clear flash message
 */
function getFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = [
            'text' => $_SESSION['flash_message'],
            'type' => $_SESSION['flash_type'] ?? 'info'
        ];
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
        return $message;
    }
    return null;
}
?>
