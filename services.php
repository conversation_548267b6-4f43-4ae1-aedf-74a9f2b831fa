<?php
/**
 * Services Page for Flori Construction Ltd Website
 * 
 * This page displays all the services offered by the company
 * with detailed descriptions and related projects.
 */

require_once 'config/config.php';

// Get all active services
$db = getDB();
$services = $db->fetchAll(
    "SELECT * FROM services WHERE is_active = 1 ORDER BY sort_order ASC"
);

// Get page content
$page_title_content = getContentBlock('services_page_title', 'Services We Provide');
$page_subtitle = getContentBlock('services_page_subtitle', 'We are committed to delivering high-quality services in these areas and more. Our goal is to provide reliable and efficient solutions that meet the unique needs of each project.');

$page_title = 'Our Services - ' . SITE_NAME;
$page_description = 'Professional construction services including civil engineering, groundworks, RC frames, basement construction, and hard landscaping in London and surrounding areas.';

include 'includes/header.php';
?>

<!-- Page Header -->
<section class="page-header" style="
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.9), rgba(29, 78, 216, 0.9)), 
                url('assets/images/services-header-bg.jpg') center/cover;
    padding: 120px 0 80px;
    color: white;
    text-align: center;
">
    <div class="container">
        <h1><?php echo escapeOutput($page_title_content); ?></h1>
        <p style="font-size: 1.2rem; max-width: 600px; margin: 20px auto 0;"><?php echo escapeOutput($page_subtitle); ?></p>
    </div>
</section>

<!-- Services Section -->
<section class="section services-detail">
    <div class="container">
        <?php foreach ($services as $index => $service): ?>
        <div class="service-detail" id="<?php echo escapeOutput($service['slug']); ?>">
            <div class="row <?php echo $index % 2 === 0 ? '' : 'reverse'; ?>">
                <div class="col-6">
                    <div class="service-content">
                        <div class="service-icon-large">
                            <i class="<?php echo escapeOutput($service['icon_class'] ?: 'fas fa-tools'); ?>"></i>
                        </div>
                        <h2><?php echo escapeOutput($service['title']); ?></h2>
                        <p class="service-short-desc"><?php echo escapeOutput($service['short_description']); ?></p>
                        
                        <?php if ($service['full_description']): ?>
                        <div class="service-full-desc">
                            <?php echo nl2br(escapeOutput($service['full_description'])); ?>
                        </div>
                        <?php endif; ?>
                        
                        <div class="service-features">
                            <h4>Key Features:</h4>
                            <ul>
                                <?php
                                // Generate some key features based on service type
                                $features = [];
                                switch (strtolower($service['title'])) {
                                    case 'civil engineering':
                                        $features = [
                                            'Infrastructure design and planning',
                                            'Site surveys and assessments',
                                            'Drainage and utilities systems',
                                            'Road and pathway construction',
                                            'Structural engineering solutions'
                                        ];
                                        break;
                                    case 'professional groundworks':
                                    case 'groundworks':
                                        $features = [
                                            'Site clearance and preparation',
                                            'Excavation and earthworks',
                                            'Foundation construction',
                                            'Drainage installation',
                                            'Concrete works and paving'
                                        ];
                                        break;
                                    case 'rc frames construction':
                                    case 'rc frames':
                                        $features = [
                                            'Reinforced concrete structures',
                                            'Multi-story building frames',
                                            'Precast concrete installation',
                                            'Structural integrity testing',
                                            'Quality assurance and compliance'
                                        ];
                                        break;
                                    case 'basement construction':
                                    case 'basements':
                                        $features = [
                                            'Waterproofing and tanking',
                                            'Underpinning and excavation',
                                            'Structural basement walls',
                                            'Ventilation and lighting',
                                            'Finishing and utilities'
                                        ];
                                        break;
                                    case 'hard landscaping':
                                        $features = [
                                            'Paving and stonework',
                                            'Retaining walls and structures',
                                            'Outdoor drainage systems',
                                            'Pathways and driveways',
                                            'Garden structures and features'
                                        ];
                                        break;
                                    default:
                                        $features = [
                                            'Professional consultation',
                                            'Quality materials and workmanship',
                                            'Project management',
                                            'Health and safety compliance',
                                            'Timely completion'
                                        ];
                                }
                                
                                foreach ($features as $feature):
                                ?>
                                <li><i class="fas fa-check"></i> <?php echo escapeOutput($feature); ?></li>
                                <?php endforeach; ?>
                            </ul>
                        </div>
                        
                        <a href="contact.php?service=<?php echo urlencode($service['title']); ?>" class="btn btn-primary">Get Quote for <?php echo escapeOutput($service['title']); ?></a>
                    </div>
                </div>
                <div class="col-6">
                    <div class="service-image">
                        <?php if ($service['image_url']): ?>
                        <img src="<?php echo escapeOutput($service['image_url']); ?>" 
                             alt="<?php echo escapeOutput($service['title']); ?>" 
                             loading="lazy">
                        <?php else: ?>
                        <img src="assets/images/service-<?php echo $service['id']; ?>.jpg" 
                             alt="<?php echo escapeOutput($service['title']); ?>" 
                             loading="lazy"
                             onerror="this.src='assets/images/service-placeholder.jpg'">
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        
        <?php if ($index < count($services) - 1): ?>
        <hr class="service-divider">
        <?php endif; ?>
        
        <?php endforeach; ?>
    </div>
</section>

<!-- Related Projects Section -->
<section class="section related-projects" style="background: #f9fafb;">
    <div class="container">
        <div class="section-header">
            <h2>Projects Showcasing Our Services</h2>
            <p>See our services in action through our completed projects</p>
        </div>
        
        <?php
        // Get some featured projects
        $featured_projects = $db->fetchAll(
            "SELECT p.*, pc.name as category_name 
             FROM projects p 
             LEFT JOIN project_categories pc ON p.category_id = pc.id 
             WHERE p.status = 'completed' 
             ORDER BY p.is_featured DESC, p.sort_order ASC 
             LIMIT 6"
        );
        ?>
        
        <div class="projects-grid">
            <?php foreach ($featured_projects as $project): ?>
            <div class="project-card">
                <div class="project-image">
                    <?php if ($project['featured_image']): ?>
                    <img src="<?php echo escapeOutput($project['featured_image']); ?>" 
                         alt="<?php echo escapeOutput($project['title']); ?>" 
                         loading="lazy">
                    <?php else: ?>
                    <img src="assets/images/project-placeholder.jpg" 
                         alt="<?php echo escapeOutput($project['title']); ?>" 
                         loading="lazy">
                    <?php endif; ?>
                </div>
                <div class="project-content">
                    <div class="project-category"><?php echo escapeOutput($project['category_name'] ?: 'Project'); ?></div>
                    <h3><?php echo escapeOutput($project['title']); ?></h3>
                    <p><?php echo escapeOutput(truncateText($project['short_description'], 100)); ?></p>
                    <a href="project.php?slug=<?php echo escapeOutput($project['slug']); ?>" class="btn btn-outline">View Project</a>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
        
        <div class="text-center mt-4">
            <a href="projects.php" class="btn btn-primary">View All Projects</a>
        </div>
    </div>
</section>

<!-- Call to Action -->
<section class="section cta" style="background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white;">
    <div class="container text-center">
        <h2>Need Professional Construction Services?</h2>
        <p>Contact our experienced team today for a free consultation and detailed quote for your project.</p>
        <div class="cta-buttons">
            <a href="contact.php" class="btn btn-secondary">Get Free Quote</a>
            <a href="tel:<?php echo escapeOutput(getCompanyInfo('phone_primary')); ?>" class="btn btn-outline" style="border-color: white; color: white;">
                Call <?php echo escapeOutput(getCompanyInfo('phone_primary')); ?>
            </a>
        </div>
    </div>
</section>

<style>
.service-detail {
    margin-bottom: 80px;
}

.service-detail .row {
    align-items: center;
    gap: 60px;
}

.service-detail .row.reverse {
    flex-direction: row-reverse;
}

.service-content {
    padding: 20px 0;
}

.service-icon-large {
    font-size: 4rem;
    color: #2563eb;
    margin-bottom: 20px;
}

.service-content h2 {
    color: #1f2937;
    margin-bottom: 15px;
}

.service-short-desc {
    font-size: 1.1rem;
    color: #6b7280;
    margin-bottom: 20px;
    font-weight: 500;
}

.service-full-desc {
    margin-bottom: 30px;
    line-height: 1.7;
}

.service-features {
    margin-bottom: 30px;
}

.service-features h4 {
    color: #1f2937;
    margin-bottom: 15px;
}

.service-features ul {
    list-style: none;
    padding: 0;
}

.service-features li {
    padding: 8px 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.service-features i {
    color: #2563eb;
    font-size: 0.9rem;
}

.service-image {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.service-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.service-image:hover img {
    transform: scale(1.05);
}

.service-divider {
    border: none;
    height: 1px;
    background: linear-gradient(to right, transparent, #e5e7eb, transparent);
    margin: 60px 0;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .service-detail .row,
    .service-detail .row.reverse {
        flex-direction: column;
        gap: 30px;
    }
    
    .service-detail {
        margin-bottom: 60px;
    }
    
    .service-icon-large {
        font-size: 3rem;
    }
    
    .service-image img {
        height: 250px;
    }
    
    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .cta-buttons .btn {
        width: 200px;
    }
}
</style>

<?php include 'includes/footer.php'; ?>
